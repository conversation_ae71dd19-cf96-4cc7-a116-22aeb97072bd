#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解析需求和设计评审检查单，提取检查项
"""

import pandas as pd
import json
import os

def parse_requirement_checklist(file_path):
    """解析需求评审检查单"""
    print("解析需求评审检查单...")
    
    df = pd.read_excel(file_path, sheet_name=0, header=None)
    
    # 从第5行开始是检查项（索引4）
    checklist_items = []
    current_category = ""
    
    for i in range(5, len(df)):  # 从第6行开始（索引5）
        row = df.iloc[i]
        
        # 检查是否是分类行（第一列不是数字，第三列有内容）
        col0 = str(row[0]).strip() if pd.notna(row[0]) else ""
        col1 = str(row[1]).strip() if pd.notna(row[1]) else ""
        col2 = str(row[2]).strip() if pd.notna(row[2]) else ""
        
        # 如果第一列不是数字且第三列有内容，可能是分类
        if col0 and not col0.isdigit() and col2 and col0 != "符合度统计：":
            current_category = col0
            print(f"发现分类: {current_category}")
            continue
        
        # 如果第一列是数字，第三列有检查项内容
        if col0.isdigit() and col2:
            item = {
                "id": col0,
                "category": current_category,
                "is_key": col1 == "是",
                "content": col2,
                "result_column": "是/否/不适用",
                "comment_column": "注释"
            }
            checklist_items.append(item)
            print(f"检查项 {col0}: {col2[:50]}...")
    
    return checklist_items

def parse_design_checklist(file_path):
    """解析设计评审检查表"""
    print("解析设计评审检查表...")
    
    df = pd.read_excel(file_path, sheet_name=0, header=None)
    
    # 从第10行开始是检查项（索引9）
    checklist_items = []
    current_category = ""
    
    for i in range(10, len(df)):  # 从第11行开始（索引10）
        row = df.iloc[i]
        
        # 检查各列内容
        col0 = str(row[0]).strip() if pd.notna(row[0]) else ""  # 序号
        col1 = str(row[1]).strip() if pd.notna(row[1]) else ""  # 关键项目
        col2 = str(row[2]).strip() if pd.notna(row[2]) else ""  # 设计分类
        col3 = str(row[3]).strip() if pd.notna(row[3]) else ""  # 检查项
        col4 = str(row[4]).strip() if pd.notna(row[4]) else ""  # 重点关注角色
        
        # 如果第三列有内容且第一列为空或不是数字，可能是分类
        if col2 and (not col0 or not col0.isdigit()) and col3:
            current_category = col2
            print(f"发现分类: {current_category}")
        
        # 如果第一列是数字，第四列有检查项内容
        if col0.isdigit() and col3 and col0 != "符合度统计：":
            item = {
                "id": col0,
                "category": current_category,
                "is_key": col1 == "★",  # 设计检查表用★标记关键项
                "content": col3,
                "focus_role": col4,
                "result_column": "是/否/不适用",
                "comment_column": "评审意见",
                "defect_level_column": "缺陷级别"
            }
            checklist_items.append(item)
            print(f"检查项 {col0}: {col3[:50]}...")
    
    return checklist_items

def main():
    """主函数"""
    print("开始解析检查单文件...")
    
    # 解析需求评审检查单
    req_file = 'docs/SWXA-QMS-RD-400 需求评审检查单 V2.2(2).xlsx'
    req_items = parse_requirement_checklist(req_file)
    
    # 保存需求检查项
    req_output = {
        "document_type": "需求评审检查单",
        "version": "V2.2",
        "total_items": len(req_items),
        "evaluation_options": ["是", "否", "不适用"],
        "items": req_items
    }
    
    with open('debug/需求评审检查项.json', 'w', encoding='utf-8') as f:
        json.dump(req_output, f, ensure_ascii=False, indent=2)
    
    print(f"需求评审检查单解析完成，共 {len(req_items)} 个检查项")
    
    # 解析设计评审检查表
    design_file = 'docs/SWXA-QMS-PD-400 软件设计评审检查表.xlsx'
    design_items = parse_design_checklist(design_file)
    
    # 保存设计检查项
    design_output = {
        "document_type": "设计评审检查表",
        "version": "PD-400",
        "total_items": len(design_items),
        "evaluation_options": ["是", "否", "不适用"],
        "items": design_items
    }
    
    with open('debug/设计评审检查项.json', 'w', encoding='utf-8') as f:
        json.dump(design_output, f, ensure_ascii=False, indent=2)
    
    print(f"设计评审检查表解析完成，共 {len(design_items)} 个检查项")
    
    print("\n解析完成!")
    print("生成的文件:")
    print("- debug/需求评审检查项.json")
    print("- debug/设计评审检查项.json")

if __name__ == "__main__":
    main()
