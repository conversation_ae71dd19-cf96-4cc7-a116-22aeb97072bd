        // 初始化页面
        function initializePage() {
            // 绑定表单提交事件
            document.getElementById('uploadForm').addEventListener('submit', handleFormSubmit);
            
            // 绑定文档类型变化事件
            document.getElementById('documentType').addEventListener('change', handleDocumentTypeChange);
            
            // 初始加载检查单信息
            handleDocumentTypeChange();
            
            // 初始化调试面板
            initializeDebugPanel();
        }

        // 处理表单提交
        async function handleFormSubmit(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const analyzeBtn = document.getElementById('analyzeBtn');
            const originalText = analyzeBtn.innerHTML;
            
            // 显示加载状态
            analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 分析中...';
            analyzeBtn.disabled = true;
            document.querySelector('.loading').style.display = 'block';
            document.getElementById('debugPanel').style.display = 'block';
            
            try {
                const response = await fetch('/analyze', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    currentResult = result;
                    displayResults(result);
                } else {
                    throw new Error(result.error || '分析失败');
                }
            } catch (error) {
                console.error('分析失败:', error);
                alert('分析失败: ' + error.message);
            } finally {
                analyzeBtn.innerHTML = originalText;
                analyzeBtn.disabled = false;
                document.querySelector('.loading').style.display = 'none';
            }
        }

        // 处理文档类型变化
        async function handleDocumentTypeChange() {
            const documentType = document.getElementById('documentType').value;
            
            try {
                const response = await fetch(`/api/checklist/${documentType}`);
                const result = await response.json();
                
                if (result.success) {
                    displayChecklistInfo(result.data);
                } else {
                    console.error('获取检查单信息失败:', result.error);
                }
            } catch (error) {
                console.error('获取检查单信息失败:', error);
            }
        }

        // 显示检查单信息
        function displayChecklistInfo(checklistData) {
            const container = document.getElementById('checklistInfo');
            
            let categoriesHtml = '';
            for (const [category, count] of Object.entries(checklistData.categories)) {
                categoriesHtml += `<span class="badge bg-secondary me-2">${category}: ${count}项</span>`;
            }
            
            container.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-clipboard-list"></i> ${checklistData.checklist_name}</h6>
                        <p class="mb-2">总检查项: <strong>${checklistData.total_items}</strong></p>
                        <div class="mb-2">
                            <small class="text-muted">分类统计:</small><br>
                            ${categoriesHtml}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle"></i> 评审说明</h6>
                        <ul class="small mb-0">
                            <li><span class="badge bg-success">是</span> - 该检查点没有发现问题</li>
                            <li><span class="badge bg-danger">否</span> - 发现问题</li>
                            <li><span class="badge bg-secondary">不适用</span> - 此检查点对该评审对象不适用</li>
                        </ul>
                    </div>
                </div>
            `;
            
            document.getElementById('checklistInfoCard').style.display = 'block';
        }

        // 显示评审结果
        function displayResults(result) {
            // 显示评审摘要
            displaySummary(result);
            
            // 显示检查项详情
            displayChecklistDetails(result);
            
            // 显示章节分析
            displaySectionsAnalysis(result);
            
            // 显示结果卡片
            document.getElementById('reviewResultCard').style.display = 'block';
        }

        // 显示评审摘要
        function displaySummary(result) {
            const container = document.getElementById('summaryContent');
            const stats = result.statistics || {};
            
            let content = `
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-success">${stats.total_items || 0}</h4>
                                <small>总检查项</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-primary">${stats.compliance_rate || 0}%</h4>
                                <small>符合率</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-warning">${stats.non_compliant_items || 0}</h4>
                                <small>不符合项</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-info">${stats.key_items || 0}</h4>
                                <small>关键项</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            if (result.summary) {
                content += `
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-file-alt"></i> 评审总结</h6>
                        </div>
                        <div class="card-body">
                            <div class="p-3 bg-light rounded">
                                ${result.summary}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = content;
        }

        // 显示检查项详情
        function displayChecklistDetails(result) {
            const container = document.getElementById('checklistContent');
            const checklistResults = result.checklist_results || [];
            
            // 按分类分组
            const groupedResults = {};
            checklistResults.forEach(item => {
                const category = item.category || '其他';
                if (!groupedResults[category]) {
                    groupedResults[category] = [];
                }
                groupedResults[category].push(item);
            });
            
            let content = '';
            for (const [category, items] of Object.entries(groupedResults)) {
                content += `
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">${category} (${items.length}项)</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th style="width: 80px;">序号</th>
                                            <th>检查项</th>
                                            <th style="width: 100px;">结果</th>
                                            <th style="width: 120px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                `;
                
                items.forEach(item => {
                    const result = item.comprehensive_result || '不适用';
                    const badgeClass = getResultBadgeClass(result);
                    
                    content += `
                        <tr>
                            <td>${item.sequence || item.id}</td>
                            <td>
                                ${item.content}
                                ${item.is_key ? '<span class="badge bg-warning ms-2">关键项</span>' : ''}
                            </td>
                            <td><span class="badge ${badgeClass}">${result}</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" onclick="showItemDetail('${item.item_id}')">
                                    <i class="fas fa-eye"></i> 详情
                                </button>
                            </td>
                        </tr>
                    `;
                });
                
                content += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = content;
        }

        // 显示章节分析
        function displaySectionsAnalysis(result) {
            const container = document.getElementById('sectionsContent');
            const sections = result.sections || [];
            
            let content = '';
            sections.forEach((section, index) => {
                content += `
                    <div class="card mb-3">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">${section.section_name}</h6>
                                <div>
                                    <span class="badge bg-success">${section.compliant_items || 0}</span>
                                    <span class="badge bg-danger">${section.non_compliant_items || 0}</span>
                                    <span class="badge bg-secondary">${section.applicable_items || 0}</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <p><strong>内容长度:</strong> ${section.content_length || 0} 字符</p>
                            <p><strong>有效内容:</strong> ${section.has_content ? '是' : '否'}</p>
                            <p><strong>适用检查项:</strong> ${section.applicable_items || 0} 项</p>
                        </div>
                    </div>
                `;
            });
            
            if (content === '') {
                content = '<div class="alert alert-info">暂无章节分析数据</div>';
            }
            
            container.innerHTML = content;
        }

        // 获取结果徽章样式
        function getResultBadgeClass(result) {
            switch(result) {
                case '是': return 'bg-success';
                case '否': return 'bg-danger';
                case '不适用': return 'bg-secondary';
                default: return 'bg-light text-dark';
            }
        }

        // 显示检查项详情
        function showItemDetail(itemId) {
            if (!currentResult || !currentResult.checklist_results) {
                alert('没有找到检查项详情');
                return;
            }
            
            const item = currentResult.checklist_results.find(i => i.item_id === itemId);
            if (!item) {
                alert('没有找到该检查项');
                return;
            }
            
            let content = `
                <div class="modal fade" id="itemDetailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">检查项详情 - ${item.item_id}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <h6>检查项内容</h6>
                                <p class="bg-light p-3 rounded">${item.content}</p>
                                
                                <h6>综合评审结果</h6>
                                <p><span class="badge ${getResultBadgeClass(item.comprehensive_result)}">${item.comprehensive_result}</span></p>
                                
                                <h6>综合分析说明</h6>
                                <p class="bg-light p-3 rounded">${item.comprehensive_explanation || '暂无说明'}</p>
                                
                                ${item.recommendations && item.recommendations.length > 0 ? `
                                    <h6>改进建议</h6>
                                    <ul>
                                        ${item.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                                    </ul>
                                ` : ''}
                                
                                ${item.section_results && item.section_results.length > 0 ? `
                                    <h6>各章节评审结果</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>章节</th>
                                                    <th>结果</th>
                                                    <th>说明</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${item.section_results.map(sr => `
                                                    <tr>
                                                        <td>${sr.section_name}</td>
                                                        <td><span class="badge ${getResultBadgeClass(sr.result)}">${sr.result}</span></td>
                                                        <td>${sr.explanation || ''}</td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                ` : ''}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 移除已存在的模态框
            const existingModal = document.getElementById('itemDetailModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // 添加新的模态框
            document.body.insertAdjacentHTML('beforeend', content);
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('itemDetailModal'));
            modal.show();
        }

        // 导出结果
        function exportResults() {
            if (!currentResult) {
                alert('没有可导出的结果');
                return;
            }
            
            // 简单的JSON导出
            const dataStr = JSON.stringify(currentResult, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `评审结果_${currentResult.project_name || 'document'}_${new Date().toISOString().slice(0, 10)}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
        }

        // 初始化调试面板
        function initializeDebugPanel() {
            // 连接SSE用于实时调试信息
            const eventSource = new EventSource('/debug-stream');
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type !== 'heartbeat') {
                        addDebugMessage(data);
                    }
                } catch (error) {
                    console.error('解析调试消息失败:', error);
                }
            };
            
            eventSource.onerror = function(error) {
                console.error('SSE连接错误:', error);
            };
        }

        // 添加调试消息
        function addDebugMessage(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date(message.timestamp * 1000).toLocaleTimeString();
            const levelClass = message.level === 'error' ? 'text-danger' : 
                              message.level === 'success' ? 'text-success' : 'text-info';
            
            const messageHtml = `<div class="progress-item">
                <span class="timestamp">[${timestamp}]</span> 
                <span class="${levelClass}">${message.message}</span>
            </div>`;
            
            debugLog.insertAdjacentHTML('beforeend', messageHtml);
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        // 切换调试面板显示
        function toggleDebugPanel() {
            const panel = document.querySelector('#debugPanel .card-body');
            if (panel.style.display === 'none') {
                panel.style.display = 'block';
                debugVisible = true;
            } else {
                panel.style.display = 'none';
                debugVisible = false;
            }
        }
