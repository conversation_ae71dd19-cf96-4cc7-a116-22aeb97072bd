{"document_type": "设计评审检查表", "version": "PD-400", "total_items": 55, "evaluation_options": ["是", "否", "不适用"], "categories": {"整体设计": 3, "架构设计": 5, "系统、子系统设计": 7, "数据库设计": 5, "接口设计": 9, "安全性设计": 6, "性能设计": 2, "可维护性设计": 7, "兼容性设计": 2, "高可用设计": 3, "复用设计": 3, "可测试性设计": 3}, "items": [{"id": "整体设计_6", "category": "整体设计", "sequence": "6", "is_key": false, "content": "是否对用户界面进行图形化设计？", "focus_role": "测试人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "整体设计_7", "category": "整体设计", "sequence": "7", "is_key": false, "content": "是否对用户界面的场景进行描述？", "focus_role": "测试人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "整体设计_8", "category": "整体设计", "sequence": "8", "is_key": false, "content": "是否描述了所有的备选方案，并进行对比分析？", "focus_role": "评审专家", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "架构设计_9", "category": "架构设计", "sequence": "9", "is_key": false, "content": "是否包含业务架构？", "focus_role": "评审专家", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "架构设计_10", "category": "架构设计", "sequence": "10", "is_key": false, "content": "是否包含技术架构？", "focus_role": "评审专家", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "架构设计_11", "category": "架构设计", "sequence": "11", "is_key": false, "content": "是否包含架构视图（逻辑试图、开发试图、部署试图、运行试图、用例试图（可裁剪）），且层次关系是否清晰？", "focus_role": "评审专家", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "架构设计_12", "category": "架构设计", "sequence": "12", "is_key": false, "content": "是否包含安全架构（威胁建模过程、安全架构试图）设计？", "focus_role": "评审专家", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "架构设计_13", "category": "架构设计", "sequence": "13", "is_key": false, "content": "是否还有其它问题？若有，请详细说明。", "focus_role": "评审专家", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "系统、子系统设计_14", "category": "系统、子系统设计", "sequence": "14", "is_key": false, "content": "是否包含系统子系统模块清晰的层次关系？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "系统、子系统设计_15", "category": "系统、子系统设计", "sequence": "15", "is_key": false, "content": "是否对功能模块进行进一步分解？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "系统、子系统设计_16", "category": "系统、子系统设计", "sequence": "16", "is_key": false, "content": "是否描述功能模块的处理流程？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "系统、子系统设计_17", "category": "系统、子系统设计", "sequence": "17", "is_key": false, "content": "是否对子系统之间调用的接口进行了详细的说明？（包括接口间调用关系、各个接口的业务流程、参数限制、特殊业务说明等）", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "系统、子系统设计_18", "category": "系统、子系统设计", "sequence": "18", "is_key": false, "content": "是否包含内外部接口设计（外部接口可有单独接口文档）？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "系统、子系统设计_19", "category": "系统、子系统设计", "sequence": "19", "is_key": false, "content": "是否考虑模块之间的低耦合，以及模块的高内聚？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "系统、子系统设计_20", "category": "系统、子系统设计", "sequence": "20", "is_key": false, "content": "是否对详细说明了各模块需要系统分配的各种资源？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "数据库设计_21", "category": "数据库设计", "sequence": "21", "is_key": false, "content": "是否进行了数据结构以及算法设计（业务流程类算法）？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "数据库设计_22", "category": "数据库设计", "sequence": "22", "is_key": false, "content": "数据结构以及算法设计是否合理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "数据库设计_23", "category": "数据库设计", "sequence": "23", "is_key": false, "content": "是否对数据库进行了详细的逻辑结构设计、物理结构设计？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "数据库设计_24", "category": "数据库设计", "sequence": "24", "is_key": false, "content": "是否详细设计了数据库信息模型？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "数据库设计_25", "category": "数据库设计", "sequence": "25", "is_key": false, "content": "是否编写了详细的数据字典？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "接口设计_26", "category": "接口设计", "sequence": "26", "is_key": false, "content": "接口设计是否考虑边界测试输入输出参数边界分析；", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "接口设计_27", "category": "接口设计", "sequence": "27", "is_key": false, "content": "接口入参和出参是否明确；", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "接口设计_28", "category": "接口设计", "sequence": "28", "is_key": false, "content": "接口设计业务逻辑是否合理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "接口设计_29", "category": "接口设计", "sequence": "29", "is_key": false, "content": "接口是否有超时设计，且超时设计是否合理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "接口设计_30", "category": "接口设计", "sequence": "30", "is_key": false, "content": "接口设计是否考虑接口负载、并发数量和性能指标？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "接口设计_31", "category": "接口设计", "sequence": "31", "is_key": false, "content": "接口设计是否考虑幂等设计？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "接口设计_32", "category": "接口设计", "sequence": "32", "is_key": false, "content": "接口设计是否考虑同时处理大量数据设计？承载数量？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "接口设计_33", "category": "接口设计", "sequence": "33", "is_key": false, "content": "接口升级、兼容性设计？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "接口设计_34", "category": "接口设计", "sequence": "34", "is_key": false, "content": "服务稳定性设计是否合理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "安全性设计_35", "category": "安全性设计", "sequence": "35", "is_key": false, "content": "是否对数据库进行了合理的安全设计？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "安全性设计_36", "category": "安全性设计", "sequence": "36", "is_key": false, "content": "是否对运用威胁建模分析模型进行安全设计？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "安全性设计_37", "category": "安全性设计", "sequence": "37", "is_key": false, "content": "是否针对系统特性进行系统安全/隐私/韧性设计？（身份及访问控制设计、系统可信保护设计、安全隔离设计、数据保护设计、隐私保护设计、韧性设计、安全管理设计、安全部署设计）", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "安全性设计_38", "category": "安全性设计", "sequence": "38", "is_key": false, "content": "是否对关键算法或逻辑进行清晰地说明？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "安全性设计_39", "category": "安全性设计", "sequence": "39", "is_key": false, "content": "是否包含不安全的算法（例如MD5、SHA1等）？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "安全性设计_40", "category": "安全性设计", "sequence": "40", "is_key": false, "content": "是否考虑安全设计（认证、通信、存储、备份等，参考公司安全红线）？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "性能设计_41", "category": "性能设计", "sequence": "41", "is_key": false, "content": "系统性能设计是否考虑？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "性能设计_42", "category": "性能设计", "sequence": "42", "is_key": false, "content": "性能设计是否合理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "可维护性设计_43", "category": "可维护性设计", "sequence": "43", "is_key": false, "content": "是否考虑可靠性设计？（FMEA设计、冗余设计、故障管理设计、过载控制设计、人因差错设计、升级不中断业务设计、故障预测预防设计、健壮性设计、容量扩充设计、程序简化设计等）", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "可维护性设计_44", "category": "可维护性设计", "sequence": "44", "is_key": false, "content": "是否考虑子系统及系统整体的故障和异常处理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "可维护性设计_45", "category": "可维护性设计", "sequence": "45", "is_key": false, "content": "是否考虑了可维护性（错误码表、日志格式、日志覆盖范围、业务监控、升级、端口清单等）？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "可维护性设计_46", "category": "可维护性设计", "sequence": "46", "is_key": false, "content": "是否进行了错误码设计且全面完整？是否有错误码清单？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "可维护性设计_47", "category": "可维护性设计", "sequence": "47", "is_key": false, "content": "是否考虑产品稳定性设计（日志容量、数据库容量、容错修复、）？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "可维护性设计_48", "category": "可维护性设计", "sequence": "48", "is_key": false, "content": "日志是否有定期清理机制并写到了合理路径下（禁止写到tmp路径）？", "focus_role": "", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "可维护性设计_49", "category": "可维护性设计", "sequence": "49", "is_key": false, "content": "产品的内置证书签发时间是否符合公司要求（20年以上）？是否考虑了到期预警机制？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "兼容性设计_50", "category": "兼容性设计", "sequence": "50", "is_key": false, "content": "产品功能升级时，是否考虑有产品升级不影响业务的设计？\n公司产品兼容性要求：\n1）大版本和小版本尽量兼容\n2）小版本必须向上兼容\n3）接口必须向上兼容\n注：大版本如：V1、V2\n小版本如:V2.1、V2.2", "focus_role": "项目经理", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "兼容性设计_51", "category": "兼容性设计", "sequence": "51", "is_key": false, "content": "详细设计时是否考虑了兼容性、健壮性、可维护性、可测试性、可扩展性、可配置性？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "高可用设计_52", "category": "高可用设计", "sequence": "52", "is_key": false, "content": "系统高可用性设计是否考虑？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "高可用设计_53", "category": "高可用设计", "sequence": "53", "is_key": false, "content": "系统高可用性设计是否合理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "高可用设计_54", "category": "高可用设计", "sequence": "54", "is_key": false, "content": "是否考虑内存满、磁盘满、cpu满、时钟不同步等对服务的影响？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "复用设计_55", "category": "复用设计", "sequence": "55", "is_key": false, "content": "是否明确了项目内容与可复用资源的对应关系", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "复用设计_56", "category": "复用设计", "sequence": "56", "is_key": false, "content": "是否考虑组件复用，是否罗列复用的公用组件清单？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "复用设计_57", "category": "复用设计", "sequence": "57", "is_key": false, "content": "采用的公司组件库中的公用组件的版本是否最新版本？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "可测试性设计_58", "category": "可测试性设计", "sequence": "58", "is_key": false, "content": "集成测试人员是否可按照设计文档编写集成测试方案与用例？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "可测试性设计_59", "category": "可测试性设计", "sequence": "59", "is_key": false, "content": "研发人员是否可按照设计文档进行后续的编码工作？", "focus_role": "开发人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "可测试性设计_60", "category": "可测试性设计", "sequence": "60", "is_key": false, "content": "是否考虑最终模块的集成方案？", "focus_role": "开发人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}]}