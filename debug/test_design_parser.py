#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设计检查表解析
"""

import os
import shutil
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.document_parser import DocumentParser
import json

def main():
    # 清除缓存
    cache_dir = 'data/parsed_templates'
    if os.path.exists(cache_dir):
        shutil.rmtree(cache_dir)
        print('缓存已清除')

    # 重新测试
    parser = DocumentParser()
    design_file = 'docs/SWXA-QMS-PD-400 软件设计评审检查表.xlsx'

    items = parser.parse_design_checklist(design_file)

    print(f'解析结果：共 {len(items)} 个检查项')

    # 按分类统计
    categories = {}
    for item in items:
        category = item['category']
        if category not in categories:
            categories[category] = 0
        categories[category] += 1

    print('分类统计:')
    for category, count in categories.items():
        print(f'  "{category}": {count} 项')

    # 显示前几个检查项
    print('\n前10个检查项:')
    for i, item in enumerate(items[:10]):
        print(f'{i+1}. [{item["category"]}] {item["content"][:50]}...')

if __name__ == "__main__":
    main()
