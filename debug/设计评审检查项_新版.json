{"document_type": "设计评审检查表", "version": "PD-400", "total_items": 55, "evaluation_options": ["是", "否", "不适用"], "categories": {"": 55}, "items": [{"id": "6", "category": "", "sequence": "6", "is_key": false, "content": "是否对用户界面进行图形化设计？", "focus_role": "测试人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "7", "category": "", "sequence": "7", "is_key": false, "content": "是否对用户界面的场景进行描述？", "focus_role": "测试人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "8", "category": "", "sequence": "8", "is_key": false, "content": "是否描述了所有的备选方案，并进行对比分析？", "focus_role": "评审专家", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "9", "category": "", "sequence": "9", "is_key": false, "content": "是否包含业务架构？", "focus_role": "评审专家", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "10", "category": "", "sequence": "10", "is_key": false, "content": "是否包含技术架构？", "focus_role": "评审专家", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "11", "category": "", "sequence": "11", "is_key": false, "content": "是否包含架构视图（逻辑试图、开发试图、部署试图、运行试图、用例试图（可裁剪）），且层次关系是否清晰？", "focus_role": "评审专家", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "12", "category": "", "sequence": "12", "is_key": false, "content": "是否包含安全架构（威胁建模过程、安全架构试图）设计？", "focus_role": "评审专家", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "13", "category": "", "sequence": "13", "is_key": false, "content": "是否还有其它问题？若有，请详细说明。", "focus_role": "评审专家", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "14", "category": "", "sequence": "14", "is_key": false, "content": "是否包含系统子系统模块清晰的层次关系？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "15", "category": "", "sequence": "15", "is_key": false, "content": "是否对功能模块进行进一步分解？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "16", "category": "", "sequence": "16", "is_key": false, "content": "是否描述功能模块的处理流程？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "17", "category": "", "sequence": "17", "is_key": false, "content": "是否对子系统之间调用的接口进行了详细的说明？（包括接口间调用关系、各个接口的业务流程、参数限制、特殊业务说明等）", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "18", "category": "", "sequence": "18", "is_key": false, "content": "是否包含内外部接口设计（外部接口可有单独接口文档）？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "19", "category": "", "sequence": "19", "is_key": false, "content": "是否考虑模块之间的低耦合，以及模块的高内聚？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "20", "category": "", "sequence": "20", "is_key": false, "content": "是否对详细说明了各模块需要系统分配的各种资源？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "21", "category": "", "sequence": "21", "is_key": false, "content": "是否进行了数据结构以及算法设计（业务流程类算法）？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "22", "category": "", "sequence": "22", "is_key": false, "content": "数据结构以及算法设计是否合理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "23", "category": "", "sequence": "23", "is_key": false, "content": "是否对数据库进行了详细的逻辑结构设计、物理结构设计？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "24", "category": "", "sequence": "24", "is_key": false, "content": "是否详细设计了数据库信息模型？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "25", "category": "", "sequence": "25", "is_key": false, "content": "是否编写了详细的数据字典？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "26", "category": "", "sequence": "26", "is_key": false, "content": "接口设计是否考虑边界测试输入输出参数边界分析；", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "27", "category": "", "sequence": "27", "is_key": false, "content": "接口入参和出参是否明确；", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "28", "category": "", "sequence": "28", "is_key": false, "content": "接口设计业务逻辑是否合理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "29", "category": "", "sequence": "29", "is_key": false, "content": "接口是否有超时设计，且超时设计是否合理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "30", "category": "", "sequence": "30", "is_key": false, "content": "接口设计是否考虑接口负载、并发数量和性能指标？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "31", "category": "", "sequence": "31", "is_key": false, "content": "接口设计是否考虑幂等设计？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "32", "category": "", "sequence": "32", "is_key": false, "content": "接口设计是否考虑同时处理大量数据设计？承载数量？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "33", "category": "", "sequence": "33", "is_key": false, "content": "接口升级、兼容性设计？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "34", "category": "", "sequence": "34", "is_key": false, "content": "服务稳定性设计是否合理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "35", "category": "", "sequence": "35", "is_key": false, "content": "是否对数据库进行了合理的安全设计？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "36", "category": "", "sequence": "36", "is_key": false, "content": "是否对运用威胁建模分析模型进行安全设计？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "37", "category": "", "sequence": "37", "is_key": false, "content": "是否针对系统特性进行系统安全/隐私/韧性设计？（身份及访问控制设计、系统可信保护设计、安全隔离设计、数据保护设计、隐私保护设计、韧性设计、安全管理设计、安全部署设计）", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "38", "category": "", "sequence": "38", "is_key": false, "content": "是否对关键算法或逻辑进行清晰地说明？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "39", "category": "", "sequence": "39", "is_key": false, "content": "是否包含不安全的算法（例如MD5、SHA1等）？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "40", "category": "", "sequence": "40", "is_key": false, "content": "是否考虑安全设计（认证、通信、存储、备份等，参考公司安全红线）？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "41", "category": "", "sequence": "41", "is_key": false, "content": "系统性能设计是否考虑？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "42", "category": "", "sequence": "42", "is_key": false, "content": "性能设计是否合理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "43", "category": "", "sequence": "43", "is_key": false, "content": "是否考虑可靠性设计？（FMEA设计、冗余设计、故障管理设计、过载控制设计、人因差错设计、升级不中断业务设计、故障预测预防设计、健壮性设计、容量扩充设计、程序简化设计等）", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "44", "category": "", "sequence": "44", "is_key": false, "content": "是否考虑子系统及系统整体的故障和异常处理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "45", "category": "", "sequence": "45", "is_key": false, "content": "是否考虑了可维护性（错误码表、日志格式、日志覆盖范围、业务监控、升级、端口清单等）？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "46", "category": "", "sequence": "46", "is_key": false, "content": "是否进行了错误码设计且全面完整？是否有错误码清单？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "47", "category": "", "sequence": "47", "is_key": false, "content": "是否考虑产品稳定性设计（日志容量、数据库容量、容错修复、）？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "48", "category": "", "sequence": "48", "is_key": false, "content": "日志是否有定期清理机制并写到了合理路径下（禁止写到tmp路径）？", "focus_role": "", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "49", "category": "", "sequence": "49", "is_key": false, "content": "产品的内置证书签发时间是否符合公司要求（20年以上）？是否考虑了到期预警机制？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "50", "category": "", "sequence": "50", "is_key": false, "content": "产品功能升级时，是否考虑有产品升级不影响业务的设计？\n公司产品兼容性要求：\n1）大版本和小版本尽量兼容\n2）小版本必须向上兼容\n3）接口必须向上兼容\n注：大版本如：V1、V2\n小版本如:V2.1、V2.2", "focus_role": "项目经理", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "51", "category": "", "sequence": "51", "is_key": false, "content": "详细设计时是否考虑了兼容性、健壮性、可维护性、可测试性、可扩展性、可配置性？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "52", "category": "", "sequence": "52", "is_key": false, "content": "系统高可用性设计是否考虑？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "53", "category": "", "sequence": "53", "is_key": false, "content": "系统高可用性设计是否合理？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "54", "category": "", "sequence": "54", "is_key": false, "content": "是否考虑内存满、磁盘满、cpu满、时钟不同步等对服务的影响？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "55", "category": "", "sequence": "55", "is_key": false, "content": "是否明确了项目内容与可复用资源的对应关系", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "56", "category": "", "sequence": "56", "is_key": false, "content": "是否考虑组件复用，是否罗列复用的公用组件清单？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "57", "category": "", "sequence": "57", "is_key": false, "content": "采用的公司组件库中的公用组件的版本是否最新版本？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "58", "category": "", "sequence": "58", "is_key": false, "content": "集成测试人员是否可按照设计文档编写集成测试方案与用例？", "focus_role": "设计人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "59", "category": "", "sequence": "59", "is_key": false, "content": "研发人员是否可按照设计文档进行后续的编码工作？", "focus_role": "开发人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}, {"id": "60", "category": "", "sequence": "60", "is_key": false, "content": "是否考虑最终模块的集成方案？", "focus_role": "开发人员", "result_column": "是/否/不适用", "comment_column": "评审意见", "defect_level_column": "缺陷级别"}]}