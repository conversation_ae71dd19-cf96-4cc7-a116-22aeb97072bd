#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的设计检查表解析，确保包含所有60个检查项
"""

import os
import shutil
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import json

def test_complete_design_parse():
    """完整解析设计检查表，确保包含所有60个项目"""
    print("完整解析设计检查表...")
    
    design_file = 'docs/SWXA-QMS-PD-400 软件设计评审检查表.xlsx'
    df = pd.read_excel(design_file, sheet_name=0, header=None)
    
    # 从第10行开始是检查项（索引9）
    checklist_items = []
    current_category = ""
    
    for i in range(10, len(df)):  # 从第11行开始（索引10）
        row = df.iloc[i]
        
        # 检查各列内容
        col0 = str(row[0]).strip() if pd.notna(row[0]) else ""  # 序号
        col1 = str(row[1]).strip() if pd.notna(row[1]) else ""  # 关键项目
        col2 = str(row[2]).strip() if pd.notna(row[2]) else ""  # 设计分类
        col3 = str(row[3]).strip() if pd.notna(row[3]) else ""  # 检查项
        col4 = str(row[4]).strip() if pd.notna(row[4]) else ""  # 重点关注角色
        
        # 跳过统计行和空行
        if col0 == "符合度统计：" or col0 == "nan" or (not col0 and not col3):
            break
        
        # 如果第三列有内容且第一列是数字，检查是否是新分类
        if col0.isdigit() and col2 and col3:
            # 如果第三列内容不为空，说明这是一个新分类的开始
            if col2.strip():
                current_category = col2
                print(f"发现分类: {current_category}")
        
        # 如果第一列是数字，第四列有检查项内容
        if col0.isdigit() and col3:
            # 如果还没有分类，设置默认分类
            if not current_category:
                current_category = "整体设计"
                print(f"设置默认分类: {current_category}")
            
            # 生成唯一ID：分类_序号
            unique_id = f"{current_category}_{col0}" if current_category else col0
            
            item = {
                "id": unique_id,
                "category": current_category,
                "sequence": col0,
                "is_key": col1 == "★",  # 设计检查表用★标记关键项
                "content": col3,
                "focus_role": col4,
                "result_column": "是/否/不适用",
                "comment_column": "评审意见",
                "defect_level_column": "缺陷级别"
            }
            checklist_items.append(item)
            print(f"检查项 {unique_id}: {col3[:50]}...")
    
    print(f"\n解析结果：共 {len(checklist_items)} 个检查项")
    
    # 按分类统计
    categories = {}
    for item in checklist_items:
        category = item['category']
        if category not in categories:
            categories[category] = 0
        categories[category] += 1
    
    print('分类统计:')
    total_items = 0
    for category, count in categories.items():
        print(f'  "{category}": {count} 项')
        total_items += count
    
    print(f'\n总计: {total_items} 项')
    
    # 检查是否有缺失的序号
    sequences = [int(item['sequence']) for item in checklist_items]
    sequences.sort()
    print(f'序号范围: {min(sequences)} - {max(sequences)}')
    
    missing = []
    for i in range(1, 61):  # 应该有1-60项
        if i not in sequences:
            missing.append(i)
    
    if missing:
        print(f'缺失的序号: {missing}')
    else:
        print('所有序号都存在')
    
    # 保存结果
    output = {
        "document_type": "设计评审检查表",
        "version": "PD-400",
        "total_items": len(checklist_items),
        "evaluation_options": ["是", "否", "不适用"],
        "categories": categories,
        "items": checklist_items
    }
    
    with open('debug/设计评审检查项_完整版.json', 'w', encoding='utf-8') as f:
        json.dump(output, f, ensure_ascii=False, indent=2)
    
    print("已保存到: debug/设计评审检查项_完整版.json")
    
    return checklist_items

def main():
    # 清除缓存
    cache_dir = 'data/parsed_templates'
    if os.path.exists(cache_dir):
        shutil.rmtree(cache_dir)
        print('缓存已清除\n')

    # 完整解析
    items = test_complete_design_parse()

if __name__ == "__main__":
    main()
