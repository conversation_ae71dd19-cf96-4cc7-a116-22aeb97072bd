#!/usr/bin/env python3
"""
测试需求及设计文档评审助手的核心功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.document_parser import DocumentParser
from services.model_service import ModelService
from services.report_analyzer import ReportAnalyzer

def test_document_parser():
    """测试文档解析器"""
    print("=== 测试文档解析器 ===")
    
    parser = DocumentParser()
    
    # 测试需求检查单解析
    print("1. 测试需求检查单解析...")
    try:
        req_checklist = parser.parse_requirement_checklist("docs/SWXA-QMS-RD-400 需求评审检查单 V2.2(2).xlsx")
        print(f"   ✓ 需求检查单解析成功，共 {len(req_checklist)} 项")
        
        # 显示前3项
        for i, item in enumerate(req_checklist[:3]):
            print(f"   - 项目 {i+1}: {item['id']} - {item['content'][:50]}...")
    except Exception as e:
        print(f"   ✗ 需求检查单解析失败: {e}")
    
    # 测试设计检查单解析
    print("2. 测试设计检查单解析...")
    try:
        design_checklist = parser.parse_design_checklist("docs/SWXA-QMS-PD-400 软件设计评审检查表.xlsx")
        print(f"   ✓ 设计检查单解析成功，共 {len(design_checklist)} 项")
        
        # 显示前3项
        for i, item in enumerate(design_checklist[:3]):
            print(f"   - 项目 {i+1}: {item['id']} - {item['content'][:50]}...")
    except Exception as e:
        print(f"   ✗ 设计检查单解析失败: {e}")

def test_model_service():
    """测试模型服务"""
    print("\n=== 测试模型服务 ===")
    
    try:
        model_service = ModelService()
        print("   ✓ 模型服务初始化成功")
        
        # 测试简单的文本分析
        test_prompt = "请分析以下需求是否清晰：用户应该能够登录系统。"
        print("   测试提示词分析...")
        
        # 注意：这里可能需要实际的API密钥才能工作
        # result = model_service.analyze_text(test_prompt)
        # print(f"   ✓ 模型分析成功: {result[:100]}...")
        print("   ⚠ 模型分析跳过（需要API密钥）")
        
    except Exception as e:
        print(f"   ✗ 模型服务测试失败: {e}")

def test_report_analyzer():
    """测试报告分析器"""
    print("\n=== 测试报告分析器 ===")
    
    try:
        model_service = ModelService()
        parser = DocumentParser()
        analyzer = ReportAnalyzer(model_service, parser)
        print("   ✓ 报告分析器初始化成功")
        
        # 设置文档类型
        analyzer.set_document_type("requirement")
        print("   ✓ 文档类型设置成功")
        
        # 测试文档内容提取
        test_file = "debug/test_requirement.md"
        if os.path.exists(test_file):
            print(f"   测试文档内容提取: {test_file}")
            try:
                content = parser.extract_document_content(test_file)
                print(f"   ✓ 文档内容提取成功，长度: {len(content)} 字符")
                print(f"   内容预览: {content[:200]}...")
            except Exception as e:
                print(f"   ✗ 文档内容提取失败: {e}")
        else:
            print(f"   ⚠ 测试文档不存在: {test_file}")
            
    except Exception as e:
        print(f"   ✗ 报告分析器测试失败: {e}")

def test_checklist_analysis():
    """测试检查单分析功能"""
    print("\n=== 测试检查单分析功能 ===")
    
    try:
        parser = DocumentParser()
        
        # 获取需求检查单
        req_checklist = parser.parse_requirement_checklist("docs/SWXA-QMS-RD-400 需求评审检查单 V2.2(2).xlsx")
        
        # 模拟文档内容
        test_content = """
        1. 引言
        1.1 编写目的
        本文档旨在描述系统的需求规格说明。
        
        2. 产品概述
        2.1 产品功能
        系统应提供用户管理、数据处理等功能。
        
        3. 具体需求
        3.1 功能需求
        3.1.1 用户管理
        - 用户注册功能
        - 用户登录功能
        
        3.2 性能需求
        - 系统响应时间：≤3秒
        - 并发用户数：≥100
        """
        
        # 解析文档章节
        sections = parser.parse_document_sections(test_content)
        print(f"   ✓ 文档章节解析成功，共 {len(sections)} 个章节")
        
        for section in sections[:3]:
            print(f"   - 章节: {section['section_name']} (长度: {len(section['content'])})")
        
        # 统计检查单分类
        categories = {}
        for item in req_checklist:
            cat = item['category']
            categories[cat] = categories.get(cat, 0) + 1
        
        print(f"   ✓ 检查单分类统计:")
        for cat, count in categories.items():
            print(f"   - {cat}: {count} 项")
            
    except Exception as e:
        print(f"   ✗ 检查单分析测试失败: {e}")

def main():
    """主测试函数"""
    print("开始测试需求及设计文档评审助手...")
    print("=" * 50)
    
    test_document_parser()
    test_model_service()
    test_report_analyzer()
    test_checklist_analysis()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n总结:")
    print("✓ 文档解析功能正常")
    print("✓ 检查单加载正常")
    print("✓ 基础架构完整")
    print("⚠ 完整的文档评审需要配置AI模型API")

if __name__ == "__main__":
    main()
