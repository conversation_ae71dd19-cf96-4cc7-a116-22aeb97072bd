#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的检查单解析方法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.document_parser import DocumentParser
import json

def test_requirement_checklist():
    """测试需求评审检查单解析"""
    print("=== 测试需求评审检查单解析 ===")
    
    parser = DocumentParser()
    req_file = 'docs/SWXA-QMS-RD-400 需求评审检查单 V2.2(2).xlsx'
    
    items = parser.parse_requirement_checklist(req_file)
    
    print(f"解析结果：共 {len(items)} 个检查项")
    
    # 按分类统计
    categories = {}
    for item in items:
        category = item['category']
        if category not in categories:
            categories[category] = 0
        categories[category] += 1
    
    print("分类统计:")
    for category, count in categories.items():
        print(f"  {category}: {count} 项")
    
    # 保存结果
    output = {
        "document_type": "需求评审检查单",
        "version": "V2.2",
        "total_items": len(items),
        "evaluation_options": ["是", "否", "不适用"],
        "categories": categories,
        "items": items
    }
    
    with open('debug/需求评审检查项_新版.json', 'w', encoding='utf-8') as f:
        json.dump(output, f, ensure_ascii=False, indent=2)
    
    print("已保存到: debug/需求评审检查项_新版.json")
    
    return items

def test_design_checklist():
    """测试设计评审检查表解析"""
    print("\n=== 测试设计评审检查表解析 ===")
    
    parser = DocumentParser()
    design_file = 'docs/SWXA-QMS-PD-400 软件设计评审检查表.xlsx'
    
    items = parser.parse_design_checklist(design_file)
    
    print(f"解析结果：共 {len(items)} 个检查项")
    
    # 按分类统计
    categories = {}
    for item in items:
        category = item['category']
        if category not in categories:
            categories[category] = 0
        categories[category] += 1
    
    print("分类统计:")
    for category, count in categories.items():
        print(f"  {category}: {count} 项")
    
    # 保存结果
    output = {
        "document_type": "设计评审检查表",
        "version": "PD-400",
        "total_items": len(items),
        "evaluation_options": ["是", "否", "不适用"],
        "categories": categories,
        "items": items
    }
    
    with open('debug/设计评审检查项_新版.json', 'w', encoding='utf-8') as f:
        json.dump(output, f, ensure_ascii=False, indent=2)
    
    print("已保存到: debug/设计评审检查项_新版.json")
    
    return items

def main():
    """主函数"""
    print("开始测试新的检查单解析方法...")
    
    # 测试需求评审检查单
    req_items = test_requirement_checklist()
    
    # 测试设计评审检查表
    design_items = test_design_checklist()
    
    print(f"\n测试完成!")
    print(f"需求评审检查项: {len(req_items)} 个")
    print(f"设计评审检查项: {len(design_items)} 个")

if __name__ == "__main__":
    main()
