from fastapi import FastAPI
from fastapi.responses import JSONResponse
from services.document_parser import DocumentParser
import uvicorn

app = FastAPI()
document_parser = DocumentParser()

@app.get("/")
async def root():
    return {"message": "Test server is running"}

@app.get("/api/checklist/{document_type}")
async def get_checklist(document_type: str):
    """获取检查单信息"""
    try:
        if document_type == "requirement":
            checklist = document_parser.parse_requirement_checklist("docs/SWXA-QMS-RD-400 需求评审检查单 V2.2(2).xlsx")
            checklist_name = "需求评审检查单"
        elif document_type == "design":
            checklist = document_parser.parse_design_checklist("docs/SWXA-QMS-PD-400 软件设计评审检查表.xlsx")
            checklist_name = "设计评审检查表"
        else:
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": "不支持的文档类型"}
            )
        
        # 按分类统计
        categories = {}
        for item in checklist:
            category = item['category']
            if category not in categories:
                categories[category] = 0
            categories[category] += 1
        
        return JSONResponse(content={
            "success": True, 
            "data": {
                "checklist_name": checklist_name,
                "total_items": len(checklist),
                "categories": categories,
                "items": checklist[:10]  # 只返回前10项作为预览
            }
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8003)
