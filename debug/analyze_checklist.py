#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析需求和设计评审检查单的结构
"""

import pandas as pd
import os
import sys

def analyze_excel_file(file_path, file_type):
    """分析Excel文件结构"""
    print(f"\n=== {file_type} ===")
    print(f"文件路径: {file_path}")
    print(f"文件存在: {os.path.exists(file_path)}")
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return None
    
    try:
        # 获取所有sheet名称
        xl_file = pd.ExcelFile(file_path)
        print(f"Sheet名称: {xl_file.sheet_names}")
        
        # 读取第一个sheet
        df = pd.read_excel(file_path, sheet_name=0, header=None)
        print(f"行数: {len(df)}, 列数: {len(df.columns)}")
        
        # 显示前15行数据
        print("前15行数据:")
        for i in range(min(15, len(df))):
            row_data = []
            for j in range(len(df.columns)):
                cell_value = df.iloc[i, j]
                if pd.isna(cell_value):
                    row_data.append("NaN")
                else:
                    row_data.append(str(cell_value))
            print(f"第{i+1}行: {row_data}")
        
        # 查找检查项开始的行
        print("\n查找检查项结构...")
        for i in range(len(df)):
            for j in range(len(df.columns)):
                cell_value = df.iloc[i, j]
                if pd.notna(cell_value):
                    cell_str = str(cell_value).strip()
                    if any(keyword in cell_str for keyword in ['检查项', '检查内容', '评审内容', '序号']):
                        print(f"可能的检查项标题行: 第{i+1}行第{j+1}列 = {cell_str}")
        
        # 保存为CSV便于查看
        csv_path = file_path.replace('.xlsx', '.csv').replace('docs/', 'debug/')
        os.makedirs('debug', exist_ok=True)
        df.to_csv(csv_path, encoding='utf-8', index=False, header=False)
        print(f"已保存CSV到: {csv_path}")
        
        return df
        
    except Exception as e:
        print(f"读取文件失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("开始分析检查单文件结构...")
    
    # 分析需求评审检查单
    req_file = 'docs/SWXA-QMS-RD-400 需求评审检查单 V2.2(2).xlsx'
    req_df = analyze_excel_file(req_file, "需求评审检查单")
    
    # 分析设计评审检查表
    design_file = 'docs/SWXA-QMS-PD-400 软件设计评审检查表.xlsx'
    design_df = analyze_excel_file(design_file, "设计评审检查表")
    
    print("\n分析完成!")

if __name__ == "__main__":
    main()
