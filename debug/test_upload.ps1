$uri = "http://localhost:8004/analyze"
$filePath = "debug/test_requirement.md"

# 创建multipart form data
$boundary = [System.Guid]::NewGuid().ToString()
$LF = "`r`n"

$bodyLines = (
    "--$boundary",
    "Content-Disposition: form-data; name=`"document_type`"$LF",
    "requirement",
    "--$boundary",
    "Content-Disposition: form-data; name=`"document_file`"; filename=`"test_requirement.md`"",
    "Content-Type: text/markdown$LF",
    (Get-Content $filePath -Raw),
    "--$boundary--$LF"
) -join $LF

try {
    $response = Invoke-RestMethod -Uri $uri -Method Post -Body $bodyLines -ContentType "multipart/form-data; boundary=$boundary"
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Response: $($_.Exception.Response)"
}
