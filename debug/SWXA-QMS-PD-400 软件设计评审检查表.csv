,,,,,,,
项目名称,,,,评审时间,,,
评审人,,,,评审人的角色,,,
说明,关键项目,设计分类,"依据每个检查点检查被评审对象，将您的评审意见填写在该检查点右边的栏目中。
如果该检查点没有发现问题，就选择“是”；
如果发现问题，则选择“否”，
如果此检查点对该评审对象不适用，则选择“不适用”。
对每个检查点如有其它内容需要说明，则记录在对应的“评审意见”一栏中。如在评审过程中发现其它问题，则记录在列表下方的“其它问题”中。
列出的缺陷级别是提供的参考指南，如果不满足检查项则缺陷级别为参考的缺陷级别。",,,,
序号,,,检查项,重点关注角色,是/否/不适用,评审意见,缺陷级别
1,,整体设计,设计是否具有可读性？文档是否齐全？是否按照模板编制？,评审专家,,,
2,,,软件功能是否与需求规格说明书保持一致，并完整体现？,评审专家,,,
3,,,设计中的术语和缩略语是否记录清楚？,评审专家,,,
4,,,是否设计描述清晰明确？,评审专家,,,
5,,,是否明确了产品运行环境（软硬件环境）和开发环境？,设计人员,,,
6,,,是否对用户界面进行图形化设计？,测试人员,,,
7,,,是否对用户界面的场景进行描述？,测试人员,,,
8,,,是否描述了所有的备选方案，并进行对比分析？,评审专家,,,
9,,架构设计,是否包含业务架构？,评审专家,,,
10,,,是否包含技术架构？,评审专家,,,
11,,,是否包含架构视图（逻辑试图、开发试图、部署试图、运行试图、用例试图（可裁剪）），且层次关系是否清晰？,评审专家,,,
12,,,是否包含安全架构（威胁建模过程、安全架构试图）设计？,评审专家,,,
13,,,是否还有其它问题？若有，请详细说明。,评审专家,,,
14,,系统、子系统设计,是否包含系统子系统模块清晰的层次关系？,设计人员,,,
15,,,是否对功能模块进行进一步分解？,设计人员,,,
16,,,是否描述功能模块的处理流程？,设计人员,,,
17,,,是否对子系统之间调用的接口进行了详细的说明？（包括接口间调用关系、各个接口的业务流程、参数限制、特殊业务说明等）,设计人员,,,
18,,,是否包含内外部接口设计（外部接口可有单独接口文档）？,设计人员,,,
19,,,是否考虑模块之间的低耦合，以及模块的高内聚？,设计人员,,,
20,,,是否对详细说明了各模块需要系统分配的各种资源？,设计人员,,,
21,,数据库设计,是否进行了数据结构以及算法设计（业务流程类算法）？,设计人员,,,
22,,,数据结构以及算法设计是否合理？,设计人员,,,
23,,,是否对数据库进行了详细的逻辑结构设计、物理结构设计？,设计人员,,,
24,,,是否详细设计了数据库信息模型？,设计人员,,,
25,,,是否编写了详细的数据字典？,设计人员,,,
26,,接口设计,接口设计是否考虑边界测试输入输出参数边界分析；,设计人员,,,
27,,,接口入参和出参是否明确；,设计人员,,,
28,,,接口设计业务逻辑是否合理？,设计人员,,,
29,,,接口是否有超时设计，且超时设计是否合理？,设计人员,,,
30,,,接口设计是否考虑接口负载、并发数量和性能指标？,设计人员,,,
31,,,接口设计是否考虑幂等设计？,设计人员,,,
32,,,接口设计是否考虑同时处理大量数据设计？承载数量？,设计人员,,,
33,,,接口升级、兼容性设计？,设计人员,,,
34,,,服务稳定性设计是否合理？,设计人员,,,
35,,安全性设计,是否对数据库进行了合理的安全设计？,设计人员,,,
36,,,是否对运用威胁建模分析模型进行安全设计？,设计人员,,,
37,,,是否针对系统特性进行系统安全/隐私/韧性设计？（身份及访问控制设计、系统可信保护设计、安全隔离设计、数据保护设计、隐私保护设计、韧性设计、安全管理设计、安全部署设计）,设计人员,,,
38,,,是否对关键算法或逻辑进行清晰地说明？,设计人员,,,
39,,,是否包含不安全的算法（例如MD5、SHA1等）？,设计人员,,,
40,,,是否考虑安全设计（认证、通信、存储、备份等，参考公司安全红线）？,设计人员,,,
41,,性能设计,系统性能设计是否考虑？,设计人员,,,
42,,,性能设计是否合理？,设计人员,,,
43,,可维护性设计,是否考虑可靠性设计？（FMEA设计、冗余设计、故障管理设计、过载控制设计、人因差错设计、升级不中断业务设计、故障预测预防设计、健壮性设计、容量扩充设计、程序简化设计等）,设计人员,,,
44,,,是否考虑子系统及系统整体的故障和异常处理？,设计人员,,,
45,,,是否考虑了可维护性（错误码表、日志格式、日志覆盖范围、业务监控、升级、端口清单等）？,设计人员,,,
46,,,是否进行了错误码设计且全面完整？是否有错误码清单？,设计人员,,,
47,,,是否考虑产品稳定性设计（日志容量、数据库容量、容错修复、）？,设计人员,,,
48,,,日志是否有定期清理机制并写到了合理路径下（禁止写到tmp路径）？,,,,
49,,,产品的内置证书签发时间是否符合公司要求（20年以上）？是否考虑了到期预警机制？,设计人员,,,
50,,兼容性设计,"产品功能升级时，是否考虑有产品升级不影响业务的设计？
公司产品兼容性要求：
1）大版本和小版本尽量兼容
2）小版本必须向上兼容
3）接口必须向上兼容
注：大版本如：V1、V2
小版本如:V2.1、V2.2",项目经理,,,
51,,,详细设计时是否考虑了兼容性、健壮性、可维护性、可测试性、可扩展性、可配置性？,设计人员,,,
52,,高可用设计,系统高可用性设计是否考虑？,设计人员,,,
53,,,系统高可用性设计是否合理？,设计人员,,,
54,,,是否考虑内存满、磁盘满、cpu满、时钟不同步等对服务的影响？,设计人员,,,
55,,复用设计,是否明确了项目内容与可复用资源的对应关系,设计人员,,,
56,,,是否考虑组件复用，是否罗列复用的公用组件清单？,设计人员,,,
57,,,采用的公司组件库中的公用组件的版本是否最新版本？,设计人员,,,
58,,可测试性设计,集成测试人员是否可按照设计文档编写集成测试方案与用例？,设计人员,,,
59,,,研发人员是否可按照设计文档进行后续的编码工作？,开发人员,,,
60,,,是否考虑最终模块的集成方案？,开发人员,,,
,,,,,,,
4,★,,,,,,
,,,,,,,
,,,,,,,
符合度统计：,,,,,,,
