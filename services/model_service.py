import os
import re
import time
import json
import asyncio
from openai import OpenAI
from typing import Dict, Any, List, Optional
from .prompt_service import PromptService


def debug_log(message, level="debug", debug_callback=None):
    if debug_callback:
        debug_callback(message, level)
    if level == "info" or level == "error":
        print(f"[{level.upper()}] {message}")
    return


class ModelService:
    def __init__(self, debug_callback=None):
        # 在测试模式下不需要真实的API客户端
        if os.getenv("TEST_MODE") == "true":
            self.client = None
        else:
            self.client = OpenAI(
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
            )
        self.nothink = os.getenv("NO_THINK", "")
        self.ai_timeout = int(os.getenv("OPENAI_TIMEOUT", 600))
        self.debug_callback = debug_callback

        # 初始化提示词服务
        self.prompt_service = PromptService()

        # 初始化MCP智能体服务
        self.mcp_agent = None
        self.mcp_enabled = os.getenv("MCP_ENABLED", "false").lower() == "true"
        asyncio.run(self.initialize_mcp_agent())

        # 初始化统计信息
        self.reset_stats()

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_tokens": 0,
            "api_calls": 0,
            "total_api_time": 0.0,
            "mcp_tool_calls": 0
        }

    async def initialize_mcp_agent(self):
        """初始化MCP智能体服务"""
        return

    def get_stats(self):
        """获取统计信息"""
        return self.stats.copy()

    def _get_system_prompt(self, chapter_outline: str, criteria_text: str, review_guide: str = None) -> str:
        """使用提示词服务生成系统提示词"""
        return self.prompt_service.get_section_analysis_prompt(
            chapter_outline=chapter_outline,
            criteria_text=criteria_text,
            review_guide=review_guide,
            mcp_enabled=self.mcp_enabled
        )

    def _clean_response(self, content: str) -> str:
        """清理推理模型的响应内容，去除思考标签"""
        # 去除 <think>...</think> 标签及其内容
        content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)

        # 去除 <thinking>...</thinking> 标签及其内容
        content = re.sub(r'<thinking>.*?</thinking>', '', content, flags=re.DOTALL)

        # 去除多余的空行
        content = re.sub(r'\n\s*\n', '\n\n', content)

        return content.strip()

    async def _handle_tool_calls(self, tool_calls, debug_log):
        """处理工具调用"""
        tool_results = []

        for tool_call in tool_calls:
            try:
                tool_name = tool_call.function.name
                arguments = json.loads(tool_call.function.arguments)

                debug_log(f"调用MCP工具: {tool_name}, 参数: {arguments}", "info")

                # 调用MCP工具
                result = await self.mcp_agent.call_tool(tool_name, arguments)
                tool_results.append(result)

                # 更新统计
                self.stats["mcp_tool_calls"] += 1

                debug_log(f"MCP工具调用完成: {tool_name}", "success")

            except Exception as e:
                debug_log(f"MCP工具调用失败: {tool_name}, 错误: {e}", "error")
                tool_results.append({
                    "success": False,
                    "error": str(e),
                    "tool": tool_name
                })

        return tool_results

    def _handle_llm_call(self, messages: str, enable_tool: bool = True) -> str:
        """批量分析单个章节对所有审查细则的符合情况"""
        debug_log(f"\n\n正在调用大模型API...", "info")

        # 准备工具列表
        tools = None
        if self.mcp_enabled and self.mcp_agent:
            tools = self.mcp_agent.get_all_tools()
            if tools:
                debug_log(f"可用MCP工具数量: {len(tools)}", "info")

        # 记录API调用开始时间
        api_start_time = time.time()

        # 构建API调用参数
        api_params = {
            "model": os.getenv("MODEL_NAME", "qwq-32b"),
            "messages": messages,
            "timeout": self.ai_timeout
        }

        # 如果有工具，添加工具参数
        if tools and enable_tool:
            api_params["tools"] = tools
            api_params["tool_choice"] = "auto"

        response = self.client.chat.completions.create(**api_params)

        # 记录API调用结束时间
        api_end_time = time.time()
        api_duration = api_end_time - api_start_time

        # 更新统计信息
        self.stats["api_calls"] += 1
        self.stats["total_api_time"] += api_duration

        # 提取token使用信息
        if hasattr(response, 'usage') and response.usage:
            if hasattr(response.usage, 'prompt_tokens'):
                self.stats["total_input_tokens"] += response.usage.prompt_tokens
            if hasattr(response.usage, 'completion_tokens'):
                self.stats["total_output_tokens"] += response.usage.completion_tokens
            if hasattr(response.usage, 'total_tokens'):
                self.stats["total_tokens"] += response.usage.total_tokens

            debug_log(
                f"API调用成功，耗时: {api_duration:.2f}秒, 输入Token: {response.usage.prompt_tokens}, 输出Token: {response.usage.completion_tokens}",
                "success")
        else:
            debug_log(f"API调用成功，耗时: {api_duration:.2f}秒", "success")

        # 检查响应结构
        if not response:
            raise ValueError("API响应为空")

        if not hasattr(response, 'choices') or not response.choices:
            debug_log(f"API响应结构异常: {response}", "error")
            raise ValueError("API响应中没有choices字段或choices为空")

        if len(response.choices) == 0:
            raise ValueError("API响应choices数组为空")

        if not response.choices[0]:
            raise ValueError("API响应choices[0]为空")

        if not hasattr(response.choices[0], 'message') or not response.choices[0].message:
            raise ValueError("API响应choices[0]没有message字段或message为空")

        if not hasattr(response.choices[0].message, 'content'):
            raise ValueError("API响应choices[0].message没有content字段")

        # 检查是否有工具调用
        message = response.choices[0].message
        if hasattr(message, 'tool_calls') and message.tool_calls:
            debug_log(f"检测到工具调用，数量: {len(message.tool_calls)}", "info")

            # 处理工具调用
            tool_results = asyncio.run(self._handle_tool_calls(message.tool_calls, debug_log))

            # 将工具调用结果添加到消息中，重新调用API
            messages.append({
                "role": "assistant",
                "content": message.content,
                "tool_calls": [
                    {
                        "id": tc.id,
                        "type": "function",
                        "function": {
                            "name": tc.function.name,
                            "arguments": tc.function.arguments
                        }
                    } for tc in message.tool_calls
                ]
            })

            # 添加工具结果
            for tool_call, result in zip(message.tool_calls, tool_results):
                messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": json.dumps(result, ensure_ascii=False)
                })

            # 重新调用API获取最终结果
            debug_log("重新调用API获取工具调用后的最终结果", "info")
            final_response = self.client.chat.completions.create(**api_params)
            raw_content = final_response.choices[0].message.content
        else:
            raw_content = message.content

        if raw_content is None:
            raise ValueError("API响应content为None")

        cleaned_content = self._clean_response(raw_content)

        debug_log(f"收到大模型响应[{len(raw_content)}字符]: {cleaned_content[:100]}...")
        return cleaned_content

    def analyze_section_batch(self, project_name: str, section_title: str, section_content: str, all_criteria: list,
                              chapter_outline: str = None, review_guide: str = None) -> Dict[str, Any]:
        """批量分析单个章节对所有审查细则的符合情况"""

        debug_log(f"开始分析章节: {section_title}")
        debug_log(f"章节内容长度: {len(section_content)} 字符")

        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            debug_log("运行在测试模式", "warning")

            # 模拟API调用统计信息
            mock_api_time = 0.5  # 模拟0.5秒的API调用时间
            mock_input_tokens = 1000  # 模拟输入token数
            mock_output_tokens = 200  # 模拟输出token数

            # 更新统计信息
            self.stats["api_calls"] += 1
            self.stats["total_api_time"] += mock_api_time
            self.stats["total_input_tokens"] += mock_input_tokens
            self.stats["total_output_tokens"] += mock_output_tokens
            self.stats["total_tokens"] += mock_input_tokens + mock_output_tokens

            debug_log(
                f"模拟API调用，耗时: {mock_api_time}秒, 输入Token: {mock_input_tokens}, 输出Token: {mock_output_tokens}",
                "success")

            mock_results = []
            for i, criterion in enumerate(all_criteria):
                mock_results.append({
                    "criterion_id": criterion.get("id", f"test_{i}"),
                    "result": "基本符合",
                    "explanation": f"[测试模式] 章节 {section_title} 对审查细则 {criterion.get('id', i)} 的评审结果"
                })
            debug_log(f"生成 {len(mock_results)} 个模拟评审结果", "success")
            return {"criteria_results": mock_results}
        # 如果章节内容为空，直接返回
        if not section_content.strip():
            # debug_log(f"章节 {section_title} 内容为空，跳过分析", "warning")
            empty_results = []
            for i, criterion in enumerate(all_criteria):
                empty_results.append({
                    "criterion_id": criterion.get("id", f"test_{i}"),
                    "result": "不符合",
                    "explanation": f" 章节 {section_title} 内容为空"
                })
            debug_log(f"为空章节生成 {len(empty_results)} 个不符合结果", "info")
            return {"criteria_results": empty_results}

        # 构建所有审查细则的文本，过滤无效数据
        criteria_text = ""
        valid_criteria_count = 0

        for i, criterion in enumerate(all_criteria, 1):
            criterion_id = str(criterion.get('id', '')).strip()
            criterion_content = str(criterion.get('content', '')).strip()
            criterion_requirements = str(criterion.get('requirements', '')).strip()

            # 跳过空的或无效的审查细则
            if (not criterion_content or
                    criterion_content in ['未指定', '未知内容', 'nan', 'NaN', '']):
                debug_log(f"跳过无效的审查细则 {i}: ID='{criterion_id}', 内容='{criterion_content[:30]}...'", "info")
                continue

            valid_criteria_count += 1
            criteria_text += f"""
审查细则 {valid_criteria_count}：
- 编号：{criterion_id}
- 审查范畴：{criterion_requirements if criterion_requirements and criterion_requirements != 'nan' else '未指定'}
- 审查内容：{criterion_content}
"""

        debug_log(f"有效审查细则数量: {valid_criteria_count} / {len(all_criteria)}")

        try:
            debug_log(f"准备调用大模型分析章节: {section_title}")
            # debug_log(f"   -----审查指南(长度={len(review_guide) if review_guide else 0} 字符) {review_guide[:50] if review_guide else ''}")
            debug_log(
                f"   -----该章节的编制大纲(长度={len(chapter_outline) if chapter_outline else 0} 字符) {chapter_outline[:50] if chapter_outline else ''}")
            debug_log(
                f"   -----该章节的报告内容(长度={len(section_content) if section_content else 0} 字符) {section_content[:50]}")

            sys_prompt = self._get_system_prompt(chapter_outline, criteria_text, review_guide)
            user_content = f"""
请对以下章节内容进行全面的合规性审查：
可研报告项目名称: {project_name}

章节标题：{section_title}

章节内容：
{section_content if section_content.strip() else '该章节内容为空'}

请按照系统提示中的JSON格式，对该章节逐一评审所有审查细则的符合情况。
"""
            messages = [
                {"role": "system", "content": sys_prompt},
                {"role": "user", "content": f"{user_content} {self.nothink}"}
            ]
            cleaned_content = self._handle_llm_call(messages)

            # 尝试解析JSON响应
            try:
                import json
                # 提取JSON部分
                json_start = cleaned_content.find('{')
                json_end = cleaned_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = cleaned_content[json_start:json_end]
                    result = json.loads(json_str)

                    # 统计评审结果
                    if "criteria_results" in result:
                        criteria_count = len(result["criteria_results"])
                        result_stats = {}
                        for criterion in result["criteria_results"]:
                            result_type = criterion.get("result", "未知")
                            result_stats[result_type] = result_stats.get(result_type, 0) + 1

                        debug_log(f"解析成功，获得 {criteria_count} 个评审结果", "success")
                        debug_log(f"结果统计: {result_stats}")

                    return result
                else:
                    raise ValueError("未找到有效的JSON格式")
            except Exception as json_error:
                debug_log(f"JSON解析失败: {json_error}", "error")
                debug_log(f"原始响应: {cleaned_content[:500]}...", "error")
                # 如果JSON解析失败，返回原始文本格式
                return {
                    "criteria_results": [{
                        "criterion_id": "parse_error",
                        "result": "不适用",
                        "explanation": f"大模型响应格式错误，原始响应：{cleaned_content[:500]}"
                    }]
                }

        except Exception as e:
            debug_log(f"API调用失败: {e}", "error")
            return {
                "criteria_results": [{
                    "criterion_id": "api_error",
                    "criterion_content": "API调用失败",
                    "result": "不适用",
                    "explanation": f"API调用失败：{str(e)}"
                }]
            }

    def _prepare_report_summary(self, all_sections: dict) -> str:
        """准备报告全文摘要"""
        summary_parts = []
        for section_title, section_content in all_sections.items():
            if section_content and section_content.strip():
                # 截取每个章节的前500字符作为摘要
                content_preview = section_content.strip()[:500]
                summary_parts.append(f"【{section_title}】: {content_preview}...")
            else:
                summary_parts.append(f"【{section_title}】: (章节内容为空)")

        return "\n\n".join(summary_parts)

    def analyze_criteria_comprehensive_batch(self, review_guide: str, outline, review_results: dict) -> list:
        """批量对所有审查细则进行全文综合分析"""
        # 从review_results中提取criterion_results
        criteria_results = review_results.get("criterion_results", [])

        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            # 模拟API调用统计信息
            mock_api_time = 1.0  # 批量处理模拟更长时间
            mock_input_tokens = 2000  # 批量处理模拟更多token
            mock_output_tokens = 500

            # 更新统计信息
            self.stats["api_calls"] += 1
            self.stats["total_api_time"] += mock_api_time
            self.stats["total_input_tokens"] += mock_input_tokens
            self.stats["total_output_tokens"] += mock_output_tokens
            self.stats["total_tokens"] += mock_input_tokens + mock_output_tokens

            debug_log(
                f"[测试模式] 模拟批量综合分析API调用，耗时: {mock_api_time}秒, 输入Token: {mock_input_tokens}, 输出Token: {mock_output_tokens}")

            batch_results = []
            for criterion in criteria_results:
                criterion_id = criterion.get("criterion_id", "unknown")
                batch_results.append({
                    "comprehensive_analysis": f"[测试模式] 对审查细则 {criterion_id} 的批量全文综合分析",
                    "overall_assessment": "基本符合",
                    "key_findings": [f"[测试模式] 审查细则 {criterion_id} 的关键发现"],
                    "recommendations": [f"[测试模式] 审查细则 {criterion_id} 的改进建议"]
                })
            return batch_results

        try:
            # 构建所有审查细则的信息
            criteria_info = []
            for criterion in criteria_results:
                criterion_id = criterion.get("criterion_id", "unknown")
                criterion_content = criterion.get("criterion_content", "")
                sections = criterion.get("sections", [])

                # 构建章节分析摘要
                section_analysis_summary = []
                for section in sections:
                    section_analysis_summary.append(
                        f"章节【{section['section_name']}】: {section['result']} - {section['explanation']}"
                    )

                criteria_info.append({
                    "criterion_id": criterion_id,
                    "criterion_content": criterion_content,
                    "section_analysis": "\n".join(section_analysis_summary) if section_analysis_summary else "暂无章节分析结果"
                })

            # 使用提示词服务生成系统提示词
            system_prompt = self.prompt_service.get_comprehensive_batch_prompt(outline=outline)

            # 构建用户提示词
            criteria_text = ""
            for i, criterion_info in enumerate(criteria_info, 1):
                criteria_text += f"""
## 审查细则 {i}
- **ID**: {criterion_info['criterion_id']}
- **内容**: {criterion_info['criterion_content']}
- **各章节分析结果**:
{criterion_info['section_analysis']}
"""

            user_prompt = f"""
请对以下所有审查细则进行批量全文综合分析：

{criteria_text}

请基于以上信息，给出每个审查细则的全文综合评审意见。
注意：必须按照JSON格式输出，并且结果数组的顺序要与输入的审查细则顺序一致。
"""

            debug_log(f"正在调用大模型进行批量全文综合分析...")
            debug_log(f"共处理 {len(criteria_results)} 个审查细则")

            # 记录API调用开始时间
            api_start_time = time.time()

            response = self.client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"{user_prompt} {self.nothink}"}
                ],
                timeout=self.ai_timeout  # 批量处理需要更长时间
            )

            # 记录API调用结束时间
            api_end_time = time.time()
            api_duration = api_end_time - api_start_time

            # 更新统计信息
            self.stats["api_calls"] += 1
            self.stats["total_api_time"] += api_duration

            # 提取token使用信息
            if hasattr(response, 'usage') and response.usage:
                if hasattr(response.usage, 'prompt_tokens'):
                    self.stats["total_input_tokens"] += response.usage.prompt_tokens
                if hasattr(response.usage, 'completion_tokens'):
                    self.stats["total_output_tokens"] += response.usage.completion_tokens
                if hasattr(response.usage, 'total_tokens'):
                    self.stats["total_tokens"] += response.usage.total_tokens

                debug_log(
                    f"批量综合分析API调用成功，耗时: {api_duration:.2f}秒, 输入Token: {response.usage.prompt_tokens}, 输出Token: {response.usage.completion_tokens}")
            else:
                debug_log(f"批量综合分析API调用成功，耗时: {api_duration:.2f}秒")

            # 清理响应内容
            raw_content = response.choices[0].message.content
            cleaned_content = self._clean_response(raw_content)

            debug_log(f"收到批量综合分析响应，长度: {len(raw_content)} 字符")

            # 解析JSON响应
            try:
                import json
                json_start = cleaned_content.find('{')
                json_end = cleaned_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = cleaned_content[json_start:json_end]
                    result = json.loads(json_str)

                    if "criteria_comprehensive_results" in result:
                        batch_results = result["criteria_comprehensive_results"]
                        debug_log(f"成功解析批量综合分析结果，共 {len(batch_results)} 个结果")
                        return batch_results
                    else:
                        raise ValueError("响应中未找到criteria_comprehensive_results字段")
                else:
                    raise ValueError("未找到有效的JSON格式")
            except Exception as json_error:
                debug_log(f"批量综合分析JSON解析失败: {json_error}", "error")
                debug_log(f"原始响应: {cleaned_content[:500]}...", "error")

                # 如果解析失败，返回默认结果
                fallback_results = []
                for criterion in criteria_results:
                    criterion_id = criterion.get("criterion_id", "unknown")
                    fallback_results.append({
                        "comprehensive_analysis": f"批量分析解析失败，原始响应：{cleaned_content[:200]}...",
                        "overall_assessment": "不适用",
                        "key_findings": ["批量分析解析失败"],
                        "recommendations": ["建议重新进行分析"]
                    })
                return fallback_results

        except Exception as e:
            debug_log(f"批量综合分析API调用失败: {e}", "error")
            # 返回默认结果
            fallback_results = []
            for criterion in criteria_results:
                criterion_id = criterion.get("criterion_id", "unknown")
                fallback_results.append({
                    "comprehensive_analysis": f"批量API调用失败：{str(e)}",
                    "overall_assessment": "不适用",
                    "key_findings": ["批量API调用失败"],
                    "recommendations": ["请检查网络连接和API配置"]
                })
            return fallback_results

    def summarize_review(self, review_results: list) -> Dict[str, Any]:
        """汇总所有评审结果"""
        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            # 模拟API调用统计信息
            mock_api_time = 0.8
            mock_input_tokens = 1500
            mock_output_tokens = 300

            # 更新统计信息
            self.stats["api_calls"] += 1
            self.stats["total_api_time"] += mock_api_time
            self.stats["total_input_tokens"] += mock_input_tokens
            self.stats["total_output_tokens"] += mock_output_tokens
            self.stats["total_tokens"] += mock_input_tokens + mock_output_tokens

            debug_log(
                f"[测试模式] 模拟汇总评审API调用，耗时: {mock_api_time}秒, 输入Token: {mock_input_tokens}, 输出Token: {mock_output_tokens}")

            return {
                "summary": f"[测试模式] 总体评审意见：\n共分析了 {len(review_results)} 个章节\n总体评审结论：基本符合要求\n主要问题：无重大问题\n改进建议：建议进一步完善细节"
            }

        # 使用提示词服务生成提示词
        prompt = self.prompt_service.get_summary_review_prompt(review_results=review_results)

        try:
            debug_log(f"正在调用大模型汇总评审结果...")
            debug_log(f"共有 {len(review_results)} 个章节的评审结果")
            # debug_log(f"汇总评审提示词：{prompt}")

            # 记录API调用开始时间
            api_start_time = time.time()

            response = self.client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": "你是一个专业的可研报告评审专家，请严格按照JSON格式回答。"},
                    {"role": "user", "content": f"{prompt} {self.nothink}"}
                ],
                timeout=self.ai_timeout  # 设置120秒超时
            )

            # 记录API调用结束时间
            api_end_time = time.time()
            api_duration = api_end_time - api_start_time

            # 更新统计信息
            self.stats["api_calls"] += 1
            self.stats["total_api_time"] += api_duration

            # 提取token使用信息
            if hasattr(response, 'usage') and response.usage:
                if hasattr(response.usage, 'prompt_tokens'):
                    self.stats["total_input_tokens"] += response.usage.prompt_tokens
                if hasattr(response.usage, 'completion_tokens'):
                    self.stats["total_output_tokens"] += response.usage.completion_tokens
                if hasattr(response.usage, 'total_tokens'):
                    self.stats["total_tokens"] += response.usage.total_tokens

                debug_log(
                    f"汇总评审API调用成功，耗时: {api_duration:.2f}秒, 输入Token: {response.usage.prompt_tokens}, 输出Token: {response.usage.completion_tokens}")
            else:
                debug_log(f"汇总评审API调用成功，耗时: {api_duration:.2f}秒", "error")

            # 清理响应内容
            raw_content = response.choices[0].message.content
            cleaned_content = self._clean_response(raw_content)

            debug_log(f"\n汇总评审-大模型汇总响应: {cleaned_content}...\n")

            # 尝试解析JSON响应
            try:
                import json
                # 提取JSON部分
                json_start = cleaned_content.find('{')
                json_end = cleaned_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = cleaned_content[json_start:json_end]
                    result = json.loads(json_str)
                    return {"summary": result}
                else:
                    raise ValueError("未找到有效的JSON格式")
            except Exception as json_error:
                debug_log(f"JSON解析失败: {json_error}", "error")
                # 如果JSON解析失败，返回原始文本格式
                return {
                    "summary": {
                        "overall_conclusion": "不适用",
                        "compliance_rate": "0%",
                        "major_issues": ["JSON解析失败"],
                        "improvement_suggestions": ["请检查大模型响应格式"],
                        "summary_text": f"汇总失败，原始响应：{cleaned_content[:500]}"
                    }
                }

        except Exception as e:
            debug_log(f"API调用失败: {e}", "error")
            return {
                "summary": {
                    "overall_conclusion": "不适用",
                    "compliance_rate": "0%",
                    "major_issues": [f"API调用失败：{str(e)}"],
                    "improvement_suggestions": ["请检查网络连接和API配置"],
                    "summary_text": f"汇总失败：{str(e)}"
                }
            }

    def analyze_section_with_checklist_item(self, project_name: str, section_name: str, section_content: str, checklist_item: Dict[str, Any]) -> Dict[str, Any]:
        """使用检查单项目分析章节"""
        debug_log(f"使用检查项分析章节: {section_name} - {checklist_item['id']}")

        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            return {
                "result": "是" if "设计" in checklist_item.get("content", "") else "不适用",
                "explanation": f"[测试模式] 章节 {section_name} 对检查项 {checklist_item['id']} 的评审结果"
            }

        # 如果章节内容为空
        if not section_content.strip():
            return {
                "result": "不适用",
                "explanation": f"章节 {section_name} 内容为空，无法评估该检查项"
            }

        try:
            # 使用提示词服务生成系统提示词
            system_prompt = self.prompt_service.get_checklist_analysis_prompt(checklist_item)

            user_prompt = f"""
请对以下章节内容进行检查项评审：

项目名称: {project_name}
章节标题: {section_name}

章节内容:
{section_content}

检查项: {checklist_item['content']}

请根据章节内容判断是否符合该检查项的要求，并给出详细说明。
"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"{user_prompt} {self.nothink}"}
            ]

            cleaned_content = self._handle_llm_call(messages, enable_tool=False)

            # 尝试解析JSON响应
            try:
                import json
                json_start = cleaned_content.find('{')
                json_end = cleaned_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = cleaned_content[json_start:json_end]
                    result = json.loads(json_str)
                    return result
                else:
                    # 如果没有JSON格式，尝试从文本中提取结果
                    return self._extract_result_from_text(cleaned_content)
            except Exception as json_error:
                debug_log(f"JSON解析失败: {json_error}", "error")
                return self._extract_result_from_text(cleaned_content)

        except Exception as e:
            debug_log(f"检查项分析失败: {e}", "error")
            return {
                "result": "不适用",
                "explanation": f"分析失败：{str(e)}"
            }

    def analyze_item_comprehensive(self, project_name: str, checklist_item: Dict[str, Any], full_document_content: str, section_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """对检查项进行全文档综合分析"""
        debug_log(f"对检查项进行全文档综合分析: {checklist_item['item_id']}")

        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            return {
                "result": "是",
                "explanation": f"[测试模式] 检查项 {checklist_item['item_id']} 的全文档综合分析",
                "overall_assessment": "是",
                "recommendations": [f"[测试模式] 检查项 {checklist_item['item_id']} 的改进建议"]
            }

        try:
            # 构建章节分析摘要
            section_summary = []
            for section_result in section_results:
                section_summary.append(
                    f"章节【{section_result['section_name']}】: {section_result['result']} - {section_result['explanation']}"
                )

            # 使用提示词服务生成系统提示词
            system_prompt = self.prompt_service.get_comprehensive_checklist_analysis_prompt()

            user_prompt = f"""
请对以下检查项进行全文档综合分析：

项目名称: {project_name}
检查项ID: {checklist_item['item_id']}
检查项内容: {checklist_item['content']}
检查项分类: {checklist_item['category']}
是否关键项: {'是' if checklist_item.get('is_key', False) else '否'}

各章节分析结果:
{chr(10).join(section_summary)}

全文档内容摘要:
{full_document_content[:2000]}...

请基于各章节的分析结果和全文档内容，给出该检查项的最终综合评审意见。
"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"{user_prompt} {self.nothink}"}
            ]

            cleaned_content = self._handle_llm_call(messages, enable_tool=False)

            # 尝试解析JSON响应
            try:
                import json
                json_start = cleaned_content.find('{')
                json_end = cleaned_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = cleaned_content[json_start:json_end]
                    result = json.loads(json_str)
                    return result
                else:
                    return self._extract_comprehensive_result_from_text(cleaned_content)
            except Exception as json_error:
                debug_log(f"JSON解析失败: {json_error}", "error")
                return self._extract_comprehensive_result_from_text(cleaned_content)

        except Exception as e:
            debug_log(f"综合分析失败: {e}", "error")
            return {
                "result": "不适用",
                "explanation": f"综合分析失败：{str(e)}",
                "overall_assessment": "不适用",
                "recommendations": ["建议重新分析"]
            }

    def generate_document_review_summary(self, review_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成文档评审总结"""
        debug_log("生成文档评审总结")

        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            return {
                "summary": f"[测试模式] {review_results['project_name']} 的{review_results['document_type']}评审总结"
            }

        try:
            # 构建统计信息
            checklist_results = review_results.get("checklist_results", [])
            total_items = len(checklist_results)
            compliant_items = len([item for item in checklist_results if item.get("comprehensive_result") == "是"])
            non_compliant_items = len([item for item in checklist_results if item.get("comprehensive_result") == "否"])

            # 使用提示词服务生成系统提示词
            system_prompt = self.prompt_service.get_document_summary_prompt(review_results['document_type'])

            user_prompt = f"""
请为以下文档评审结果生成总结：

项目名称: {review_results['project_name']}
文档类型: {review_results['document_type']}
检查单: {review_results['checklist_name']}

统计信息:
- 总检查项: {total_items}
- 符合项: {compliant_items}
- 不符合项: {non_compliant_items}
- 符合率: {round(compliant_items/max(total_items,1)*100, 2)}%

请生成一个简洁的评审总结。
"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"{user_prompt} {self.nothink}"}
            ]

            cleaned_content = self._handle_llm_call(messages, enable_tool=False)

            return {"summary": cleaned_content}

        except Exception as e:
            debug_log(f"总结生成失败: {e}", "error")
            return {"summary": f"总结生成失败：{str(e)}"}

    def _extract_result_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取评审结果"""
        # 简单的文本解析逻辑
        text_lower = text.lower()

        if "是" in text or "符合" in text or "满足" in text:
            result = "是"
        elif "否" in text or "不符合" in text or "不满足" in text:
            result = "否"
        else:
            result = "不适用"

        return {
            "result": result,
            "explanation": text[:500]  # 截取前500字符作为说明
        }

    def _extract_comprehensive_result_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取综合分析结果"""
        # 简单的文本解析逻辑
        text_lower = text.lower()

        if "是" in text or "符合" in text or "满足" in text:
            result = "是"
            assessment = "是"
        elif "否" in text or "不符合" in text or "不满足" in text:
            result = "否"
            assessment = "否"
        else:
            result = "不适用"
            assessment = "不适用"

        return {
            "result": result,
            "explanation": text[:500],
            "overall_assessment": assessment,
            "recommendations": ["建议根据具体情况进行改进"]
        }
