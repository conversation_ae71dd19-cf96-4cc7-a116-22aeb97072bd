import json
import os
import uuid
import shutil
from datetime import datetime
from typing import List, Dict, Any, Optional

class ReportService:
    """报告管理服务"""
    
    def __init__(self):
        self.data_dir = "data"
        self.reports_file = os.path.join(self.data_dir, "reports.json")
        self.uploads_dir = "uploads"
        self._ensure_data_dir()
        self._init_reports_file()
    
    def _ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        if not os.path.exists(self.uploads_dir):
            os.makedirs(self.uploads_dir)
    
    def _init_reports_file(self):
        """初始化报告文件"""
        if not os.path.exists(self.reports_file):
            self._save_reports([])
    
    def _load_reports(self) -> List[Dict[str, Any]]:
        """加载报告数据"""
        try:
            with open(self.reports_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _save_reports(self, reports: List[Dict[str, Any]]):
        """保存报告数据"""
        with open(self.reports_file, 'w', encoding='utf-8') as f:
            json.dump(reports, f, ensure_ascii=False, indent=2)
    
    def create_report(self, name: str, file_path: str, topic_id: str = "", 
                     description: str = "") -> Dict[str, Any]:
        """创建新报告记录"""
        reports = self._load_reports()
        
        report = {
            "id": str(uuid.uuid4()),
            "name": name,
            "file_path": file_path,
            "topic_id": topic_id,
            "description": description,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0
        }
        
        reports.append(report)
        self._save_reports(reports)
        return report
    
    def get_reports(self, topic_id: str = None) -> List[Dict[str, Any]]:
        """获取报告列表，可按专题过滤"""
        reports = self._load_reports()
        
        if topic_id:
            reports = [report for report in reports if report.get('topic_id') == topic_id]
        
        return reports
    
    def get_report(self, report_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取报告"""
        reports = self._load_reports()
        return next((report for report in reports if report['id'] == report_id), None)
    
    def update_report(self, report_id: str, **kwargs) -> Optional[Dict[str, Any]]:
        """更新报告"""
        reports = self._load_reports()
        
        for i, report in enumerate(reports):
            if report['id'] == report_id:
                # 更新字段
                for key, value in kwargs.items():
                    if key in ['name', 'topic_id', 'description']:
                        report[key] = value
                report['updated_at'] = datetime.now().isoformat()
                
                reports[i] = report
                self._save_reports(reports)
                return report
        
        return None
    
    def delete_report(self, report_id: str) -> bool:
        """删除报告"""
        reports = self._load_reports()
        
        # 找到要删除的报告
        report_to_delete = None
        for report in reports:
            if report['id'] == report_id:
                report_to_delete = report
                break
        
        if not report_to_delete:
            return False

        # 删除相关的评审记录
        from services.review_service import ReviewService
        review_service = ReviewService()
        reviews = review_service.get_reviews(report_id=report_id)
        for review in reviews:
            review_service.delete_review(review['id'])
            print(f"已删除评审记录: {review['id']}")

        # 删除报告相关的JSON文件
        json_files_to_delete = [
            os.path.join(self.data_dir, f"report_content_{report_id}.json"),
            os.path.join(self.data_dir, f"report_outline_{report_id}.json"),
            os.path.join(self.data_dir, f"report_guide_{report_id}.json"),
            os.path.join(self.data_dir, f"report_criteria_{report_id}.json")
        ]

        for json_file in json_files_to_delete:
            if os.path.exists(json_file):
                try:
                    os.remove(json_file)
                    print(f"已删除JSON文件: {json_file}")
                except Exception as e:
                    print(f"删除JSON文件失败: {e}")

        # 删除文件
        if os.path.exists(report_to_delete['file_path']):
            try:
                os.remove(report_to_delete['file_path'])
                # 同时删除对应的.md文件
                md_path = os.path.splitext(report_to_delete['file_path'])[0] + '.md'
                if os.path.exists(md_path):
                    os.remove(md_path)

                # 删除对应的JSON缓存文件
                filename = os.path.basename(report_to_delete['file_path'])
                name_without_ext = os.path.splitext(filename)[0]
                json_cache_path = os.path.join("data", "parsed_reports", f"{name_without_ext}.json")
                if os.path.exists(json_cache_path):
                    os.remove(json_cache_path)
                    print(f"已删除报告缓存文件: {json_cache_path}")

            except Exception as e:
                print(f"删除文件失败: {e}")
        
        # 从列表中删除
        reports = [report for report in reports if report['id'] != report_id]
        self._save_reports(reports)
        return True
    
    def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """保存上传的文件"""
        file_path = os.path.join(self.uploads_dir, filename)
        
        # 如果文件已存在，生成新的文件名
        if os.path.exists(file_path):
            name, ext = os.path.splitext(filename)
            counter = 1
            while os.path.exists(file_path):
                new_filename = f"{name}_{counter}{ext}"
                file_path = os.path.join(self.uploads_dir, new_filename)
                counter += 1
        
        with open(file_path, 'wb') as f:
            f.write(file_content)
        
        return file_path
