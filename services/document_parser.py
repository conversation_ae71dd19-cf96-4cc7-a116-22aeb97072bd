from docx import Document
import pandas as pd
from typing import Dict, List, Any, Tuple
import re
import requests
import os
import json
import hashlib

class DocumentParser:
    def __init__(self):
        self.outline = None
        self.review_criteria = None
        # 缓存目录
        self.cache_dir = "data"
        self.parsed_reports_dir = os.path.join(self.cache_dir, "parsed_reports")
        self.parsed_templates_dir = os.path.join(self.cache_dir, "parsed_templates")
        self._ensure_cache_dirs()        
    def _ensure_cache_dirs(self):
        """确保缓存目录存在"""
        os.makedirs(self.parsed_reports_dir, exist_ok=True)
        os.makedirs(self.parsed_templates_dir, exist_ok=True)

    def _get_file_hash(self, file_path: str) -> str:
        """获取文件的MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            print(f"计算文件哈希失败: {e}")
            return ""

    def _get_report_cache_path(self, pdf_path: str) -> str:
        """获取报告缓存文件路径"""
        filename = os.path.basename(pdf_path)
        name_without_ext = os.path.splitext(filename)[0]
        return os.path.join(self.parsed_reports_dir, f"{name_without_ext}.json")

    def _get_template_cache_path(self, template_path: str, template_type: str) -> str:
        """获取模板缓存文件路径"""
        filename = os.path.basename(template_path)
        name_without_ext = os.path.splitext(filename)[0]
        return os.path.join(self.parsed_templates_dir, f"{template_type}_{name_without_ext}.json")

    def _save_report_cache(self, pdf_path: str, report_data: Dict[str, Any]):
        """保存报告解析结果到缓存"""
        cache_path = self._get_report_cache_path(pdf_path)
        try:
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            print(f"报告解析结果已缓存到: {cache_path}")
        except Exception as e:
            print(f"保存报告缓存失败: {e}")

    def _load_report_cache(self, pdf_path: str) -> Dict[str, Any]:
        """从缓存加载报告解析结果"""
        cache_path = self._get_report_cache_path(pdf_path)
        if not os.path.exists(cache_path):
            return None

        try:
            with open(cache_path, 'r', encoding='utf-8') as f:
                cached_data = json.load(f)

            # 检查文件是否有变化
            current_hash = self._get_file_hash(pdf_path)
            if cached_data.get('file_hash') == current_hash:
                print(f"从缓存加载报告解析结果: {cache_path}")
                return cached_data
            else:
                print(f"文件已变化，缓存失效: {cache_path}")
                return None
        except Exception as e:
            print(f"加载报告缓存失败: {e}")
            return None

    def _save_template_cache(self, template_path: str, template_type: str, template_data: Any):
        """保存模板解析结果到缓存"""
        cache_path = self._get_template_cache_path(template_path, template_type)
        try:
            cache_data = {
                'file_hash': self._get_file_hash(template_path),
                'template_type': template_type,
                'data': template_data,
                'cached_at': os.path.getmtime(template_path)
            }
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            print(f"模板解析结果已缓存到: {cache_path}")
        except Exception as e:
            print(f"保存模板缓存失败: {e}")

    def _load_template_cache(self, template_path: str, template_type: str) -> Any:
        """从缓存加载模板解析结果"""
        cache_path = self._get_template_cache_path(template_path, template_type)
        if not os.path.exists(cache_path):
            return None

        try:
            with open(cache_path, 'r', encoding='utf-8') as f:
                cached_data = json.load(f)

            # 检查文件是否有变化
            current_hash = self._get_file_hash(template_path)
            if cached_data.get('file_hash') == current_hash:
                print(f"从缓存加载{template_type}解析结果: {cache_path}")
                return cached_data.get('data')
            else:
                print(f"模板文件已变化，缓存失效: {cache_path}")
                return None
        except Exception as e:
            print(f"加载模板缓存失败: {e}")
            return None

    def get_pdf_from_url(self, pdf_path: str) -> str:
        all_text = ""
        try:
            # 调用API服务解析PDF
            with open(pdf_path, 'rb') as file:
                response = requests.post(
                    os.getenv('PDF_SERVICE_URL'),
                    files={'file': (os.path.basename(pdf_path), file)},
                    timeout=180
                )

            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    all_text = result['markdown']
                else:
                    print(f"API解析失败: {result.get('error', '未知错误')}")
            else:
                print(f"API请求失败: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"调用PDF解析API失败: {e}")
        return all_text

    def extract_report_content(self, pdf_path: str) -> Dict[str, Any]:
        """统一的报告内容提取方法，返回完整的报告数据结构"""
        # 1. 尝试从缓存加载
        cached_data = self._load_report_cache(pdf_path)
        if cached_data:
            print(f"从缓存加载PDF内容: {cached_data.get('report_name', '未知')}")
            return cached_data

        # 2. 如果缓存不存在或失效，进行解析
        print(f"开始解析PDF文件: {pdf_path}")

        # 初始化章节结构，添加资质证书章节
        sections = {}

        project_name = ""

        # 3. 先尝试读取与PDF文件同名的.md文件
        md_path = os.path.splitext(pdf_path)[0] + '.md'
        all_text = ""

        if os.path.exists(md_path):
            print(f"找到同名markdown文件: {md_path}")
            try:
                with open(md_path, 'r', encoding='utf-8') as f:
                    all_text = f.read()
                print(f"成功读取markdown文件，内容长度: {len(all_text)} 字符")
            except Exception as e:
                print(f"读取markdown文件失败: {e}")
                all_text = ""

        # 4. 如果没有.md文件或读取失败，则调用get_pdf_from_url方法
        if not all_text.strip():
            print("未找到或无法读取markdown文件，调用PDF解析API...")
            all_text = self.get_pdf_from_url(pdf_path)

            # 5. 将获取的文本存储为同目录下的.md扩展名文件
            if all_text.strip():
                try:
                    with open(md_path, 'w', encoding='utf-8') as f:
                        f.write(all_text)
                    print(f"已将解析结果保存到: {md_path}")
                except Exception as e:
                    print(f"保存markdown文件失败: {e}")

        if not all_text.strip():
            print("PDF文本提取失败...")
            # 返回空的报告数据结构
            return {
                'file_hash': self._get_file_hash(pdf_path),
                'report_name': os.path.splitext(os.path.basename(pdf_path))[0],
                'short_name': '',
                'report_date': '',
                'size': 0,
                'sections': [
                    {
                        'section_name': section_name,
                        'section_content': content
                    }
                    for section_name, content in sections.items()
                ]
            }

        # 6. pdf_path中的文档名称
        project_name = os.path.splitext(os.path.basename(pdf_path))[0]

        # 7. 使用新的markdown解析方法
        sections = self._parse_markdown_content(all_text, sections)

        # 8. 生成简称
        short_name = self._extract_short_name(project_name)

        # 9. 构建完整的报告数据结构
        report_data = {
            'file_hash': self._get_file_hash(pdf_path),
            'report_name': project_name,
            'short_name': short_name,
            'report_date': self._extract_report_date(all_text),
            'size': len(all_text),
            'sections': [
                {
                    'section_name': section_name,
                    'section_content': content
                }
                for section_name, content in sections.items()
            ]
        }

        # 10. 保存到缓存
        self._save_report_cache(pdf_path, report_data)
        print(f"报告解析完成并已缓存: {project_name}")

        return report_data

    def parse_pdf(self, pdf_path: str) -> Tuple[str,Dict[str, str]]:
        """解析PDF文件，提取章节内容（保持向后兼容）"""
       
        report_data = self.extract_report_content(pdf_path)

        # 将列表格式的sections转换为字典格式以保持向后兼容
        sections_list = report_data.get('sections', [])
        sections_dict = {}
        for section in sections_list:
            sections_dict[section['section_name']] = section['section_content']

        return report_data.get('report_name', ''), sections_dict

    def _extract_short_name(self, project_name: str) -> str:
        """从项目名称提取简称"""
        return project_name

    def _extract_report_date(self, text: str) -> str:
        """从文本中提取报告日期"""
        import re

        # 匹配日期格式
        date_patterns = [
            r'(\d{4}年\d{1,2}月)',
            r'(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{4}/\d{1,2}/\d{1,2})',
            r'(\d{4}\.\d{1,2}\.\d{1,2})'
        ]

        for pattern in date_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)

        return ""

    def _parse_markdown_content(self, markdown_text: str, sections: Dict[str, str]) -> Dict[str, str]:
        """解析markdown内容，支持多种章节标题格式的robust解析"""
        lines = markdown_text.split('\n')
        current_section = None
        current_content = []
        found_chapters = set()  # 记录已找到的章节
        before_chapter = "" # 章节之前的内容

        print(f"开始解析markdown内容，共 {len(lines)} 行")

        # 逐行解析，使用统一的章节识别方法
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            # if line_stripped ==  '2）线路部分抗灾能力分析':
            #     print (f"找到章节: {line}")

            # 跳过空行和图片标记
            if not line_stripped or line_stripped.startswith('!'):
                if current_section:
                    current_content.append(line)
                continue

            # 使用统一的章节标题识别方法
            is_chapter, matched_chapter = self._identify_chapter_title(line_stripped)

            if is_chapter:
                # 保存当前章节内容
                if current_section and current_content:
                    new_content = '\n'.join(current_content).strip()
                    if new_content:
                        # 如果章节已有内容，追加而不是覆盖
                        if sections.get(current_section, None):
                            sections[current_section] += '\n\n' + new_content
                        else:
                            sections.update({current_section: new_content})
                        print(f"保存章节内容: {current_section}, 长度: {len(sections[current_section])}")

                # 开始新章节
                current_section = matched_chapter
                current_content = []
                found_chapters.add(matched_chapter)
                print(f"识别到章节: {matched_chapter}")
                continue

            # 添加内容到当前章节
            if current_section:
                current_content.append(line)
            else:
                # 如果还没有找到任何章节，归类到：前置内容
                if current_section != "前置内容":
                    current_section = "前置内容"
                    current_content = []
                current_content.append(line)

        # 保存最后一个章节的内容
        if current_section and current_content:
            new_content = '\n'.join(current_content).strip()
            if new_content:
                # 如果章节已有内容，追加而不是覆盖
                if sections.get(current_section, None):
                    sections[current_section] += '\n\n' + new_content
                else:
                    sections.update({current_section: new_content})
                print(f"保存最后章节内容: {current_section}, 长度: {len(sections[current_section])}")

        print(f"标准解析完成，找到 {len(found_chapters)} 个章节: {list(found_chapters)}")

        return sections

    def _identify_chapter_title(self, line: str, level : str = "#") -> tuple[bool, str]:
        """统一的章节标题识别方法，支持多种格式并结合编制大纲"""
        line = line.strip()

        if line.startswith(level):
            title_text = line.lstrip(level).strip()  # 去除所有 '#' 并清理前后空格

            return True, f"{title_text}"
 
        return False, line

    def _extract_project_name(self, text: str) -> str:
        """从文本中提取项目名称"""
        lines = text.split('\n')
        project_name_pattern = re.compile(r'^项目名称:\s*(.*)$')

        for line in lines:
            match = project_name_pattern.match(line.strip())
            if match:
                return match.group(1).strip()

        return ""

    def _extract_chapter_keywords(self, title: str) -> list:
        """提取章节标题的关键词"""
        import re
        # 移除数字编号和标点符号，提取关键词
        cleaned_title = re.sub(r'^\d+\.?\s*', '', title)  # 移除开头的数字编号
        cleaned_title = re.sub(r'[^\u4e00-\u9fa5a-zA-Z]', ' ', cleaned_title)  # 只保留中英文
        keywords = [word.strip() for word in cleaned_title.split() if len(word.strip()) > 1]
        return keywords
    def parse_requirement_checklist(self, file_path: str) -> List[Dict[str, Any]]:
        """解析需求评审检查单"""
        print(f"解析需求评审检查单: {file_path}")

        # 检查缓存
        cached_data = self._load_template_cache(file_path, "requirement_checklist")
        if cached_data:
            return cached_data

        try:
            df = pd.read_excel(file_path, sheet_name=0, header=None)

            # 从第5行开始是检查项（索引4）
            checklist_items = []
            current_category = ""

            for i in range(5, len(df)):  # 从第6行开始（索引5）
                row = df.iloc[i]

                # 检查是否是分类行（第一列不是数字，第三列有内容）
                col0 = str(row[0]).strip() if pd.notna(row[0]) else ""
                col1 = str(row[1]).strip() if pd.notna(row[1]) else ""
                col2 = str(row[2]).strip() if pd.notna(row[2]) else ""

                # 跳过统计行
                if col0 == "符合度统计：" or col0 == "nan":
                    break

                # 检查是否是分类行
                if col0 and not col0.isdigit() and col2 and col0 not in ["符合度统计："]:
                    # 特殊处理：如果第三列内容与第一列相同，说明是分类标题
                    if col0 == col2 or (col0 in ["清晰性", "完整性", "一致性", "可追踪性", "可检验性", "可修改性", "接口", "安全需求", "其他"]):
                        current_category = col0
                        print(f"发现分类: {current_category}")
                        continue

                # 如果第一列是数字，第三列有检查项内容
                if col0.isdigit() and col2:
                    # 生成唯一ID：分类_序号
                    unique_id = f"{current_category}_{col0}" if current_category else col0

                    item = {
                        "id": unique_id,
                        "category": current_category,
                        "sequence": col0,
                        "is_key": col1 == "是",
                        "content": col2,
                        "result_column": "是/否/不适用",
                        "comment_column": "注释"
                    }
                    checklist_items.append(item)
                    print(f"检查项 {unique_id}: {col2[:50]}...")

            # 保存到缓存
            self._save_template_cache(file_path, "requirement_checklist", checklist_items)

            return checklist_items

        except Exception as e:
            print(f"解析需求评审检查单失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def parse_design_checklist(self, file_path: str) -> List[Dict[str, Any]]:
        """解析设计评审检查表"""
        print(f"解析设计评审检查表: {file_path}")

        # 检查缓存
        cached_data = self._load_template_cache(file_path, "design_checklist")
        if cached_data:
            return cached_data

        try:
            df = pd.read_excel(file_path, sheet_name=0, header=None)

            # 从第6行开始是检查项（索引5）
            checklist_items = []
            current_category = "整体设计"

            # 手动添加前5项（这些项在Excel中的位置特殊）
            first_five_items = [
                {"seq": "1", "content": "设计是否具有可读性？文档是否齐全？是否按照模板编制？", "role": "评审专家"},
                {"seq": "2", "content": "软件功能是否与需求规格说明书保持一致，并完整体现？", "role": "评审专家"},
                {"seq": "3", "content": "设计中的术语和缩略语是否记录清楚？", "role": "评审专家"},
                {"seq": "4", "content": "是否设计描述清晰明确？", "role": "评审专家"},
                {"seq": "5", "content": "是否明确了产品运行环境（软硬件环境）和开发环境？", "role": "设计人员"}
            ]

            for item_data in first_five_items:
                unique_id = f"{current_category}_{item_data['seq']}"
                item = {
                    "id": unique_id,
                    "category": current_category,
                    "sequence": item_data['seq'],
                    "is_key": False,
                    "content": item_data['content'],
                    "focus_role": item_data['role'],
                    "result_column": "是/否/不适用",
                    "comment_column": "评审意见",
                    "defect_level_column": "缺陷级别"
                }
                checklist_items.append(item)
                print(f"检查项 {unique_id}: {item_data['content'][:50]}...")

            for i in range(5, len(df)):  # 从第6行开始（索引5）
                row = df.iloc[i]

                # 检查各列内容
                col0 = str(row[0]).strip() if pd.notna(row[0]) else ""  # 序号
                col1 = str(row[1]).strip() if pd.notna(row[1]) else ""  # 关键项目
                col2 = str(row[2]).strip() if pd.notna(row[2]) else ""  # 设计分类
                col3 = str(row[3]).strip() if pd.notna(row[3]) else ""  # 检查项
                col4 = str(row[4]).strip() if pd.notna(row[4]) else ""  # 重点关注角色

                # 跳过标题行
                if col0 == "序号" or col3 == "检查项":
                    continue

                # 跳过统计行和空行
                if col0 == "符合度统计：" or col0 == "nan" or (not col0 and not col3):
                    break

                # 如果第三列有内容且第一列是数字，检查是否是新分类
                if col0.isdigit() and col2 and col3:
                    # 如果第三列内容不为空，说明这是一个新分类的开始
                    if col2.strip():
                        current_category = col2
                        print(f"发现分类: {current_category}")

                # 如果第一列是数字，第四列有检查项内容
                if col0.isdigit() and col3:
                    # 如果还没有分类，设置默认分类
                    if not current_category:
                        current_category = "整体设计"
                    # 生成唯一ID：分类_序号
                    unique_id = f"{current_category}_{col0}" if current_category else col0

                    # 检查是否已存在相同序号的项目（避免重复）
                    existing_sequences = [item['sequence'] for item in checklist_items]
                    if col0 in existing_sequences:
                        print(f"跳过重复项: {unique_id}")
                        continue

                    item = {
                        "id": unique_id,
                        "category": current_category,
                        "sequence": col0,
                        "is_key": col1 == "★",  # 设计检查表用★标记关键项
                        "content": col3,
                        "focus_role": col4,
                        "result_column": "是/否/不适用",
                        "comment_column": "评审意见",
                        "defect_level_column": "缺陷级别"
                    }
                    checklist_items.append(item)
                    print(f"检查项 {unique_id}: {col3[:50]}...")

            # 保存到缓存
            self._save_template_cache(file_path, "design_checklist", checklist_items)

            return checklist_items

        except Exception as e:
            print(f"解析设计评审检查表失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def extract_document_content(self, document_path: str) -> Dict[str, Any]:
        """提取文档内容（支持Word和PDF）"""
        print(f"提取文档内容: {document_path}")

        if document_path.lower().endswith('.pdf'):
            # PDF文档
            project_name, sections_dict = self.parse_pdf(document_path)
            sections_list = []
            for section_name, section_content in sections_dict.items():
                sections_list.append({
                    "section_name": section_name,
                    "section_content": section_content
                })

            return {
                "document_name": project_name or os.path.splitext(os.path.basename(document_path))[0],
                "sections": sections_list
            }

        elif document_path.lower().endswith(('.docx', '.doc')):
            # Word文档
            try:
                doc = Document(document_path)
                document_name = os.path.splitext(os.path.basename(document_path))[0]

                # 提取标题和内容
                sections = []
                current_section = {"section_name": "文档开头", "section_content": ""}

                for paragraph in doc.paragraphs:
                    text = paragraph.text.strip()
                    if not text:
                        continue
                    if text == "6.4.1. 监控组件适配":
                        continue

                    # 检查是否是标题（基于样式或格式）
                    if self._is_heading(paragraph):
                        # 保存当前章节
                        if current_section["section_content"].strip():
                            sections.append(current_section)

                        # 开始新章节
                        current_section = {
                            "section_name": text,
                            "section_content": ""
                        }
                    else:
                        # 添加到当前章节内容
                        current_section["section_content"] += text + "\n"

                # 添加最后一个章节
                if current_section["section_content"].strip():
                    sections.append(current_section)

                return {
                    "document_name": document_name,
                    "sections": sections
                }

            except Exception as e:
                print(f"解析Word文档失败: {e}")
                return {"document_name": "", "sections": []}

        else:
            print(f"不支持的文档格式: {document_path}")
            return {"document_name": "", "sections": []}

    def _is_heading(self, paragraph) -> bool:
        """判断段落是否是标题"""
        # 检查样式名称
        if paragraph.style.name.startswith('Heading'):
            return True

        # 检查字体大小（标题通常字体较大）
        if paragraph.runs:
            for run in paragraph.runs:
                if run.font.size and run.font.size.pt > 12:
                    return True

        # 检查是否加粗
        if paragraph.runs:
            for run in paragraph.runs:
                if run.bold:
                    return True

        # 检查文本模式（如"第一章"、"1."等）
        text = paragraph.text.strip()
        import re
        if re.match(r'^第[一二三四五六七八九十\d]+章|^\d+\.|\d+\.\d+', text):
            return True

        return False