
import os

class Config:
    def __init__(self):
        return
    def OPENAI_TIMEOUT(self):
        return int(os.getenv("OPENAI_TIMEOUT", 600))
    def MCP_ENABLED(self):
        return os.getenv("MCP_ENABLED", "false").lower() == "true"
    def OPENAI_API_KEY(self):
        return os.getenv("OPENAI_API_KEY")
    def OPENAI_API_BASE(self):
        return os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
    def TEST_MODE(self):
        return os.getenv("TEST_MODE", "true").lower() == "true"
    def MODEL_NAME(self):
        return os.getenv("MODEL_NAME", "qwen3-32b")
    def PDF_SERVICE_URL(self):
        return os.getenv("PDF_SERVICE_URL", "")
    def NO_THINK(self):
        return os.getenv("NO_THINK", "")
    def REQUIREMENT_CHECKLIST(self):
        return os.getenv("REQUIREMENT_CHECKLIST", "docs\SWXA-QMS-RD-400 需求评审检查单 V2.2(2).xlsx")
    def DESIGN_CHECKLIST(self):
        return os.getenv("DESIGN_CHECKLIST", "docs\SWXA-QMS-PD-400 软件设计评审检查表.xlsx")
    def PROMPT_SECTION_ANALYSIS(self):
        return os.getenv("PROMPT_SECTION_ANALYSIS", "templates/prompts/section_analysis_prompt2.md")
    def PROMPT_COMPREHENSIVE_BATCH(self):
        return os.getenv("PROMPT_COMPREHENSIVE_BATCH", "templates/prompts/comprehensive_batch_prompt.md")
    def PROMPT_SUMMARY_REVIEW(self):
        return os.getenv("PROMPT_SUMMARY_REVIEW", "templates/prompts/summary_review_prompt.md")
        