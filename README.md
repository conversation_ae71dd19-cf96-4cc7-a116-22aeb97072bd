# 需求及设计文档评审助手
1.修改当前python项目（可研报告评审助手），实现需求及设计文档评审助手
2.需求文档评审依据当前目录下的文件：docs/SWXA-QMS-RD-400 需求评审检查单 V2.2.xlsx
3.设计文档评审依据当前目录下的文件：docs/SWXA-QMS-PD-400 软件设计评审检查表.xlsx
4.同样需要逐个章节的评审，依据“检查单”的检查项，判断每个章节的适用性，并在所有章节评审完毕后，从全文档的角度评审每个“检查项”。
5.对每个检查项的检查结果，可以分为如下3种结果：
- 如果该检查点没有发现问题，就选择“是”；
- 如果发现问题，则选择“否”，
- 如果此检查点对该评审对象不适用，则选择“不适用”
6.取消报告专题管理
7.取消省份管理
8.取消多个文档的汇总分析
9.根据功能需求，优化提示词

# 优化
## 1.PDF章节提取
1.extract_report_content方法和章节提取的解析优化为层次结构，输出如下结构：
'sections': [
                {
                    'section_name': section_name,
                    'section_content': content
                }