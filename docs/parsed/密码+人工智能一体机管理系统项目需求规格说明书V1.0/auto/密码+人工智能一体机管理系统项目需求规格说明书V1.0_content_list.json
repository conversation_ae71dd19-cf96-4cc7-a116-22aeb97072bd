[{"type": "text", "text": "密码+人工智能一体机管理系统项目需求规格说明书", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "V1.0 ", "page_idx": 0}, {"type": "text", "text": "三未信安科技股份有限公司2025 年 04 月 03 日", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "版本修订说明", "text_level": 1, "page_idx": 1}, {"type": "table", "img_path": "images/05c18f48ecc1786f3c683cd207f0f6a6e47d5e1c6e64204955af7df252b35cde.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>版本</td><td>修改内容</td><td>修改人</td><td>修改日期</td><td>审核人</td><td>发布日期</td></tr><tr><td>V1.0</td><td>创建</td><td>万军</td><td>2025.04.03</td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 1}, {"type": "text", "text": "目录", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "目录.......  \n1. 简介 ......... ....... .......7  \n1.1. 术语和缩写 . ....................................................................................................................... ....... 7  \n1.2. 参考资料 ..................................................................................................................................................... 8  \n2. 产品描述 .... ....................................................... .......8  \n3. 用户业务场景分析... ................................................  \n4. 与现有产品差异 ....... 9  \n5. 约束与限制 ...............................................................................................................................................10  \n6. 需求详细描述 .. .............................................................................................................. ...10  \n6.1. 产品运行环境 ....... ........................................................................................................................... ...... 10  \n6.2. 功能清单 .. ......................................................................................................................................... 11  \n6.3. 功能说明 ........ ........................................................................................................................................ 11  \n6.4. 监控组件 . .. 12  \n6.4.1.监控组件适配. ……   \nPR-F-1001 监控组件安装适配 .. ............................................................................................................................ 12  \nPR-F-1002 监控组件功能测试 .... ..................................................................................................................... 13  \n6.4.2.监控组件功能扩展.   \nPR-F-1101 AI 一体机特殊指标整理 ........................................................................................................................... 13  \nPR-F-1102 AI 一体机特殊指标获取 ..... .................................................................................................... ....... 13  \n6.5. 管理系统相关需求 . ........................................................................ ... 14  \n6.5.1. secuLlama 服务管理.  \n6.2.1.1 secuLlama 服务启动 ...... ..................................................................................................... ...... 14  \nsecuLlama 服务启动 ................................................................................................................................................... 14  \n6.2.1.2 secuLlama 服务停止 ....................................................................................................................................... 14  \nsecuLlama 服务停止.... ............................................................................................... ....... 14  \n6.2.1.3 secuLlama 服务状态查看 ............................................................................................................................... 15  \nsecuLlama 服务状态查看 ........................................................................................................................................... 15  \n6.2.1.4 secuLlama 服务版本查看 . ............................................................................................. ..... 15  \nsecuLlama 服务版本查看 ........................................................................................................................................... 15  \n6.5.2.模型管理..   \n6.5.2.1. 查看正在运行的模型.. ...................................................... ..... 16  \n查看正在运行的模型...... ........................................................................................................................................ 16  \n6.5.3. 监控管理.  \n6.5.3.1. 监控面板开发. .... 17  \n监控面板开发... ................................................................................................................. ...... 17  \n6.5.3.2. AI 一体机特殊指标梳理... ............................................................................................................................ 17  \nAI 一体机特殊指标梳理.. ... 18  \n6.5.3.3. 告警管理--本迭代由于不考虑数据库问题，暂不开发. ... 18", "page_idx": 2}, {"type": "text", "text": "告警管理... ... 18", "page_idx": 3}, {"type": "text", "text": "通过内嵌监控组件的告警管理列表，实现告警功能，包含告警规则、告警联系人、告警列表、邮件服务器配  \n置、告警历史等。....... ................... ......... ................................. .................... ......... ..... 18  \n6.5.4.1. 基于 Ukey 的认证........ ........................................................................................................................... 18  \n基于 Ukey 的认证... .......................................................................................................................... 18  \n6.5.4.2. 基于配置文件的认证.. ................................................................................................... .... 19  \n基于配置文件的认证...... ....................................................................................................................................... 19  \n.6. 智能应用统一门户相关需求 ................................................................................................................... 19  \n6.6.1.基础网关相关需求...  \n6.6.1.1. 用户身份和用户类型 ................................................................................................................................... 20  \n用户身份和用户类型.. ................................................................. .. 20  \n6.6.1.2. 门户管理服务 API 代理和鉴权.................................................................................................................... 20  \n门户管理服务 API 代理和鉴权.................................................................................................................................. 20  \n6.6.1.3. 门户访问代理和鉴权... ..................................................................................................... ..... 21  \n门户访问代理和鉴权 ................................................................................................................................................. 21  \n6.6.1.4. 应用访问代理和鉴权... ................................................................................................................... 21  \n应用访问代理和鉴权..... ......................................................................................................................... 21  \n6.6.2.门户管理员管理.  \n6.6.2.1. 新增门户管理员.. ................................................................................................................................ 22  \n新增门户管理员......................................................................................................................................................... 22  \n6.6.2.2. 编辑门户管理员........................................................................................................................................... 22  \n编辑门户管理员..................... .................................................................................................................................... 22  \n6.6.2.3. 删除门户管理员........................................................................................................................................... 23  \n删除门户管理员......................................................................................................................................................... 23  \n6.6.2.4. 门户管理员列表........................................................................................................................................... 23  \n门户管理员列表......................................................................................................................................................... 23  \n6.6.2.5. 门户管理员登录.... ................................................................................................................................... 24  \n门户管理员登录......................................................................................................................................................... 24  \n6.6.2.6. 门户管理员退出........................................................................................................................................... 24  \n门户管理员退出.... ................................................................................................................ ..... 24  \n6.6.3.门户用户管理.. .. 25  \n6.6.3.1. 新增用户....................................................................................................................................................... 25  \n新增用户..... ............................................................................................................................... 25  \n6.6.3.2. 编辑用户....................................................................................................................................................... 26  \n编辑用户..... .............................................................................................................................................. 26  \n6.6.3.3. 删除用户.. .... 26  \n删除用户...... .......................................................................................................................................... 26  \n6.6.3.4. 门户用户列表.... ................................................................................................... .... 26  \n门户用户列表..... ........................................................................................................................................ 26  \n6.6.3.5. 门户用户登录....... ................................................................................................................................. ...... 27  \n门户用户登录..... ....................................................................................................................... ..... 27  \n6.6.3.6. 门户用户退出............................................................................................................................................... 28  \n门户用户退出.... ........... .......... .............................................. ... 28", "page_idx": 3}, {"type": "text", "text": "6.6.3.7. 启用门户用户... ....... 28启用门户用户.......... ................................................................................................................................. 286.6.3.8. 禁用门户用户............................................................................................................................................... 29禁用门户用户.... ........................................................................................... ...... 296.6.4.1. 创建门户....................................................................................................................................................... 29创建门户...... ........ 296.6.4.2. 编辑门户信息............................................................................................................................................... 30编辑门户信息........... ............................................................................................................................................... 306.6.4.3. 门户列表... .... 30门户列表..................................................................................................................................................................... 306.6.4.4. 启动门户....................................................................................................................................................... 31门户列表.... .... 316.6.4.5. 停止门户....................................................................................................................................................... 31停止门户......... ................................................................................................................................................ 316.6.4.6. 删除门户....................................................................................................................................................... 32删除门户...... ............................................................................................................................................... 326.6.4.7. 跳转管理端................................................................................................................................................... 32跳转管理端................................................................................................................................................................. 326.6.4.8. 跳转门户....................................................................................................................................................... 33跳转管理端..... ................................................................................................ ........ 336.6.4.9. 首页配置....................................................................................................................................................... 33首页配置..................................................................................................................................................................... 336.6.4.10. 访问控制配置.. ................................................................................... ...... 34访问控制配置............................................................................................................................................................. 346.6.5. Dify 容器化多实例部署改造.. … 6.6.5.1. Dify 多实例部署&网络隔离&目录隔离 . ...... 34Dify 多实例部署&网络隔离&目录隔离 ........................................................................................... .......... 346.6.5.2. Dify 实例连接外部数据库................................................................................................ 错误!未定义书签。Dify 实例连接外部数据库...... ................................................................... ....错误!未定义书签。6.6.5.3. Dify 实例连接外部 Redis ......................................................................................... .......错误!未定义书签。Dify 实例连接外部 Redis ............................................................................................................... 错误!未定义书签。6.6.5.4. 初始化供应商和模型 ......................................................................................................... ............. 35初始化供应商和模型.... ..................................................................................................... ........ 356.6.5.5. Dify 实例自动化部署... ................................................................................ ...... 35Dify 实例自动化部署 .........................................................................................................................6.6.6.应用市场课未定义书签。6.6.6.1. 应用模板管理... .................................................................. ...错误!未定义书签。应用模板管理.... ........................................................................................... .....错误!未定义书签。6.6.6.2. 应用模板列表..... ...错误!未定义书签。应用模板列表..... ...错误!未定义书签。6.6.6.3. 应用安装.......................................................................................................................... 错误!未定义书签。应用安装..... ............................... ..错误!未定义书签。", "page_idx": 4}, {"type": "text", "text": "", "text_level": 1, "page_idx": 5}, {"type": "text", "text": "6.6.7.1. 访问门户首页... .. 35  \n访问门户首页. .35  \n6.6.7.2. 用户信息展示.. .. 36  \n门户用户信息. ............................................................................................................. ... 36  \n6.6.7.3. 门户首页对话. .... 37  \n门户首页对话..... ... 37  \n6.6.7.4. 应用探索（门户应用列表） .. 37  \n门户应用列表.... .. 37  \n6.6.7.5. 应用对话页.. .. 38  \n应用对话页... ..... 38  \n6.6.7.6. 用户收藏应用、取消收藏应用. ..错误!未定义书签。  \n用户收藏应用、取消收藏. .错误!未定义书签。  \n6.7. 接口需求 .... ... 46  \n6.8. 界面需求 ... ... 46  \n6.9. 性能需求 ... ... 46  \n6.10. 可靠性/可用性需求 . ... 46  \n6.11. 安全性需求... .... 47  \n6.11.1．数据脱敏..   \n6.11.2.数据围栏组件适配..   \n6.12. 可维护性需求.. ... 48  \n6.13. 工作状态需求.... ..... 48  \n6.14. 结构需求.. .. 49  \n6.15. 环保需求 ...... ...... 49  \n6.16. 认证需求.. .... 50  \n6.17. 用户文档需求. .... 50  \n6.18. 客户特殊需求.... .... 51  \n6.19. 法律法规要求.. .... 51  \n6.20. 国家及行业标准要求. ... 51  \n6.21. 失效模式分析(参见《设计失效模式和影响分析(DFMEA)库》).. . 51  \n6.22. 其他需求.... . 51", "page_idx": 5}, {"type": "text", "text": "1. 简介", "text_level": 1, "page_idx": 6}, {"type": "text", "text": "1.1. 术语和缩写 ", "text_level": 1, "page_idx": 6}, {"type": "table", "img_path": "images/0d09588d5b2d22d9207a22193adad385739f180c2d7f48fc19dc4c7ae5842ff9.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>编号</td><td>名词</td><td>说明</td></tr><tr><td>1</td><td>CryptoAI OneSystem</td><td>密码+人工智能一体机管理系统</td></tr><tr><td>2</td><td>secuLlama</td><td>基于lama 添加安全访问策略之后的大模型运行框</td></tr><tr><td>3</td><td>VLLM</td><td>Python库，大模型推理引擎。</td></tr><tr><td>3</td><td>secuLlm</td><td>自研大模型运行框架，实现多vLLM 进程服务管理及 API请求转发，并添加安全访问策略。</td></tr><tr><td>5</td><td>Dify</td><td>Dify是一款开源的大语言模型(LLM)应用开发平台。 它融合了后端即服务（Backend as Service)和 LLMOps 的理念，使开发者可以快速搭建生产级的生成式AI 应用</td></tr><tr><td>6</td><td>知识库</td><td>知识库（Knowledge）是一系列文档（Documents）的 集合，一个文档内可能包含多组内容分段（Chunks）， 知识库可以被整体集成至一个应用中作为检索上下文 使用，用户可以将企业内部文档、FAQ、规范信息等 内容上传至知识库，知识库会自动进行结构化处理。 当 LLM 接收到用户的问题后，将首先基于关键词在 知识库内检索内容。知识库将根据关键词，召回相关 度排名较高的内容区块，向LLM 提供关键上下文以 辅助其生成更加精准的回答</td></tr><tr><td>6</td><td>应用</td><td>应用是指基于GPT 等大语言模型构建的实际场景应 用。通过创建应用，用户可以将智能AI 技术应用于 特定的需求，应用类型包括：聊天助手、文档生成应 用、Agent、对话流、工作流。</td></tr><tr><td>7 8</td><td>聊天助手应用 文本生成应用</td><td>基于LLM 构建对话式交互的助手，通过交互式界面， 采用一问一答模式与用户持续对话，可以用在客户服 务、在线教育、医疗保健、金融服务等领域。这些应 用可以帮助组织提高工作效率、减少人工成本和提供 更好的用户体验。 面向文本生成类任务的助手，例如撰写故事、文本分</td></tr><tr><td>9</td><td>Agent 应用</td><td>类、翻译等 能够分解任务、推理思考、调用工具的对话式智能助 手</td></tr><tr><td>10</td><td>对话流应用</td><td>适的应定等流程的多轮对话场景，具有记忆功</td></tr><tr><td>11</td><td>工作流应用</td><td>适用于自动化、批处理等单轮生成类任务的场景的应 用编排方式</td></tr></table></body></html>", "page_idx": 6}, {"type": "text", "text": "1.2. 参考资料", "text_level": 1, "page_idx": 7}, {"type": "text", "text": "", "page_idx": 7}, {"type": "text", "text": "2. 产品描述", "text_level": 1, "page_idx": 7}, {"type": "text", "text": "三未信安基于密码安全与AI 技术的深厚积累，针对大模型本地化部署中的安全挑战，倾力打造\"密码 $^ +$ \"人工智能一体机产品。", "page_idx": 7}, {"type": "text", "text": "产品深度融合国产硬件、大模型RAG 框架及全栈式安全防护能力，围绕“硬件 $^ +$ 软件 $^ +$ 服务”提供身份认证、数据隔离、内容过滤、模型保护等核心功能模块。面向各行业智能化升级需求，形成集智能问答交互、文档分析处理、数据治理优化等多功能于一体的“开箱即用”解决方案。通过对算力资源、算法模型与安全机制的统筹设计，全面兼顾业务效率与合规要求，真正实现模型部署易、管理省、安全强，让企业在智能化转型中稳步前行。", "page_idx": 7}, {"type": "text", "text": "三未信安AI一体机管理系统用于管理、监控 AI一体机，利用该系统，可以将后台执行的功能通过页面来提供，方便客户使用、管理、监控一体机。该管理系统的整体架构图如下，核心为应用层：", "page_idx": 7}, {"type": "image", "img_path": "images/a92b2e7c1973f73b263eefb6eb1dfb3a0a619b3352f11463c1ae310ac63c320f.jpg", "img_caption": [], "img_footnote": [], "page_idx": 7}, {"type": "text", "text": "说明：管理系统不涉及利用大模型进行推理、会话等业务，仅用来提供运维功能。具体的业务管理、运营功能敬请期待后续产品。", "page_idx": 8}, {"type": "text", "text": "3. 用户业务场景分析", "text_level": 1, "page_idx": 8}, {"type": "text", "text": "1. 运维场景：通过简单的操作即可将大模型轻松运行起来，提供服务，大大降低运维人员的运维压力，降低AI 使用的难度；2. 监控场景：通过管理系统可以实现对一体机的全面、实时监控，可以快速、准确的检测到设备的实时状态，在问题出现时，能够快速定位问题原因；3. 性能优化场景：由于降低了运维压力，且提供了实时的监控，运维人员可以投入更多的精力到大模型性能提升上，通过直观的监控，可能更加清晰的找到性能瓶颈点；4. 数据脱敏场景：提供一系列针对敏感数据的识别和处置方案，应用多种隐私合规标准，对原始数据进行分级打标、判断敏感级别和实施相应的脱敏处理；5. 数据围栏场景：提供输入输出内容风险检测的能力，帮助用户发现色情、暴力、惊悚、敏感、禁限、辱骂等风险内容或元素，降低人工审核成本，提升内容质量；6. 数据隔离场景：通过多 Dify 实例的管理功能 $^ +$ 租户管理，为每个租户构建独立知识库，确保数据互不干扰，实现知识库隔离。同时，依据不同租户需求，通过精细化的AI 应用权限管理，保障各租户在使用AI 应用时的数据安全与权限边界明确。", "page_idx": 8}, {"type": "text", "text": "4. 与现有产品差异", "text_level": 1, "page_idx": 8}, {"type": "table", "img_path": "images/3d623933f6d99bd2903f93bf0fb1702529c47b6310fec74e30690c9412c15d43.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>现有产品功能点</td><td>新需求差异描述</td><td>备注</td></tr><tr><td>当前AI一体机没有管理页面，只 能通过后台命令进行管理;且不具 备监控功能。</td><td>增加管理页面，支持通过页面进行简单 的运维操作；支持监控管理，实现对一 体机状态的实时监控。</td><td></td></tr><tr><td>当前AI一体机不具有敏感数据脱 敏处理的能力，存在敏感数据泄漏 风险。</td><td>支持多种数据格式，支持多种敏感数据 识别规则，支持多种脱敏算法。</td><td></td></tr><tr><td>当前AI大模型仅支持简单的关键 词屏蔽或黑白名单规则，无法处理 复杂语义或变体风险内容。</td><td>针对不良价值观、涉黄、违法犯罪等安 全问题，降低大模型拒答率，支持风险 问题的正向引导和纠偏。</td><td></td></tr></table></body></html>", "page_idx": 8}, {"type": "text", "text": "5. 约束与限制", "text_level": 1, "page_idx": 9}, {"type": "text", "text": "", "page_idx": 9}, {"type": "text", "text": "6. 需求详细描述", "text_level": 1, "page_idx": 9}, {"type": "text", "text": "本项目将分多次迭代实现，第一次迭代重点实现基础的运维与监控，dify服务管理，智能应用门户，并集成数据脱敏组件与内容安全检测组件：", "page_idx": 9}, {"type": "text", "text": "1. 监控组件的适配（以及开发，考虑是否扩展指标）", "page_idx": 9}, {"type": "text", "text": "2. secuL<PERSON>a/secuLlm 服务的启停--支持参数传入", "page_idx": 9}, {"type": "text", "text": "3. 模型的展示  \n4. 监控的管理  \n5. 安全认证  \n6. 数据脱敏  \n7. 数据围栏  \n8.智能应用门户", "page_idx": 9}, {"type": "text", "text": "6.1. 产品运行环境", "text_level": 1, "page_idx": 9}, {"type": "table", "img_path": "images/03d2c7457489549ee8b906784ee2c46f439101b03f0017729c9e3a671e198ebd.jpg", "table_caption": ["软件环境："], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0001</td><td>操作系统</td><td>Ubuntu 22.04.4 LTS</td><td>高</td></tr><tr><td>PR-E-0002</td><td>JAVA库</td><td>OpenJDK 17</td><td>高</td></tr><tr><td>PR-E-0003</td><td>Nginx</td><td>sansec/3.20.2</td><td>高</td></tr><tr><td></td><td></td><td>使用公用组件 JCE-5.3.3.22 版本； 监控组件使用2.1版本？？</td><td>高</td></tr><tr><td></td><td>Python</td><td>python3.10+</td><td></td></tr><tr><td></td><td>Go</td><td></td><td></td></tr></table></body></html>", "page_idx": 9}, {"type": "text", "text": "硬件配置：", "page_idx": 9}, {"type": "table", "img_path": "images/3399f580ae341a8dc0f6fa414692b8d6ea5109d9b96e27a4817e944ce2aa5f28.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0004</td><td>AI一体机整 体配置</td><td>CPU: Intel Xeon Gold 6430 32 核 64 线程*2 内存：32GB DDR4 RECC*8 硬盘1：480GSATA*2 硬盘2：3.84TNVME*2 GPU: GeForce RTX 4090 24GB *4 电源：2000W*2 Ukey : XT200*3 密码卡：版本待定？？62卡最新版</td><td>高</td></tr></table></body></html>", "page_idx": 10}, {"type": "text", "text": "6.2. 功能清单", "text_level": 1, "page_idx": 10}, {"type": "text", "text": "1. 监控组件的适配（以及开发，考虑是否扩展指标）", "page_idx": 10}, {"type": "text", "text": "2. secuLlama 服务的启停--支持参数传入", "page_idx": 10}, {"type": "text", "text": "3. 模型的展示  \n4. 监控的管理  \n5. 安全认证  \n6. 数据脱敏  \n7. 数据围栏  \n8.智能应用门户", "page_idx": 10}, {"type": "text", "text": "6.3. 功能说明", "text_level": 1, "page_idx": 10}, {"type": "image", "img_path": "images/6f0374ba79d2a16e8f5dd753be65759cb7ec8c10e8190c3ca56b011cc99f346c.jpg", "img_caption": [], "img_footnote": [], "page_idx": 11}, {"type": "text", "text": "6.4. 监控组件", "text_level": 1, "page_idx": 11}, {"type": "text", "text": "6.4.1. 监控组件适配 ", "page_idx": 11}, {"type": "text", "text": "PR-F-1001 监控组件安装适配 ", "text_level": 1, "page_idx": 11}, {"type": "table", "img_path": "images/6dceb7148ea78952ecf4904da3330147bc1a6dfe826491bdf2ae45ea6112644b.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-1001</td><td>需求名称</td><td>监控组件安装适配</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">1、需要在AI一体机上安装监控组件以及所需的基础运行环境； 2、保证监控组件在AI一体机上稳定运行； 3、提供可监控指标的汇总表。</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入輸出約束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">监控组件安装成功，并可稳定运行</td></tr></table></body></html>", "page_idx": 11}, {"type": "table", "img_path": "images/6a6226377009861f4ce2d3bfcb4ec1cc82ad38056050f188c074557ae92102cb.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 12}, {"type": "text", "text": "PR-F-1002 监控组件功能测试", "text_level": 1, "page_idx": 12}, {"type": "table", "img_path": "images/fec01cb9736242720e329c52afc37db940ddf3da4b31e2f9642e6df9649be3a8.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-1002</td><td>需求名称</td><td>监控组件功能测试</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">1.测试监控组件是否可以正确获取各项指标； 2.整理指标集，确定哪些指标需要在管理系统上展示，对外输出指标汇总表；</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">1、输出指标汇总表； 2、监控组件各指标采集正常；</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 12}, {"type": "text", "text": "6.4.2. 监控组件功能扩展", "text_level": 1, "page_idx": 12}, {"type": "text", "text": "PR-F-1101 AI 一体机特殊指标整理", "text_level": 1, "page_idx": 12}, {"type": "table", "img_path": "images/0bb87bf19039b31b1578be5eda2a1bbda6e7aaa1a9fe5d891ada26d3e272ac00.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-1101</td><td>需求名称</td><td>AI一体机特殊指标整理</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">收集需要单独采集的指标-显卡的数量、显存、温度、进程的使用情况，还需要考虑 国产GPU的兼容问题，将指标汇总至指标汇总表； gpu列表，每个gpu对象包含：温度、显存、已用显存、</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">输出指标汇总表</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 12}, {"type": "text", "text": "PR-F-1102 AI 一体机特殊指标获取", "text_level": 1, "page_idx": 12}, {"type": "table", "img_path": "images/2441a3e5318196d724757f171ef0e57feb86ea8057d7e898527af0da6abe05bf.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-1102</td><td>需求名称</td><td>AI一体机特殊指标获取</td><td>优先级</td><td>高</td></tr></table></body></html>", "page_idx": 12}, {"type": "table", "img_path": "images/37dfa0aed369b862a49593f5706dcd807c3691a275611d8be7d2085ec9138374.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求描述</td><td>根据整理的需要单独处理的指标，研究、评估采集方式，并考虑是否可以集成到监 控组件，如果需要集成到监控组件，需要支持通过监控组件获取指标。</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>如果可以集成至监控组件，可以通过监控组件获取对应的指标。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 13}, {"type": "text", "text": "6.5. 管理系统相关需求", "text_level": 1, "page_idx": 13}, {"type": "text", "text": "6.5.1. secuLlama 服务/secullm 服务管理", "page_idx": 13}, {"type": "table", "img_path": "images/af307e935657eb706a9f0c8c153a4df939581bff6e04a13f24e99ef5b0efeb52.jpg", "table_caption": ["6.2.1.1 secuLlama/secullm 服务服务启动"], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-2001</td><td>需求名称</td><td>secuLlama服务启动</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">1、收集 secuLlama 启动时支持配置哪些参数，支持通过页面可以实现对 secuLlama 的服务启动，并支持传参；如果传参为空，则使用默认的配置参数；从当前启动脚 本上来看，支持的参数：监听端口、SSL 配置(tls，gmtls，none)、证书的配置 2、如果服务已经启动，则禁止重复操作；</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">3、支持后台修改配置：修改配置后需要删除原来容器，重启启动容器。 不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">可以通过页面完成 secuLlama 服务的启动，并通过后台可以看到该进程存在；此外，</td><td></td><td></td></tr><tr><td></td><td colspan=\"3\">支持启动时传递配置参数。该接口无需压测。 不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 13}, {"type": "text", "text": "6.2.1.2 secuLlama 服务/secullm 服务停止", "text_level": 1, "page_idx": 13}, {"type": "table", "img_path": "images/e19283fe5b951a6adb9eb2655616de1f175d4c528b6f7919b3b4539059214183.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-2002</td><td>需求名称</td><td>secuLlama服务停止</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">1、支持通过页面可以实现对 secuLlama/secullm 服务的服务停止； 2、如果服务已经停止，则禁止重复操作； 3、停止服务时，需要考虑是否有模型正在运行；弹窗需要提示：停止服务，会停</td><td></td><td></td></tr></table></body></html>", "page_idx": 13}, {"type": "table", "img_path": "images/05e4ef457ba547ca9bba287bac1e42edca47957f48732565041fdea4cefa511c.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td></td><td></td></tr><tr><td>业务流程</td><td></td></tr><tr><td>输入输出约束 不涉及</td><td>可以通过页面完成 secuLlama 服务的停止，并通过后台可以看到该进程已停止，该</td></tr><tr><td>验收标准</td><td>接口无需压测。 不涉及</td></tr><tr><td>其它说明</td><td></td></tr></table></body></html>", "page_idx": 14}, {"type": "text", "text": "6.2.1.3 secuLlama 服务/secullm 服务状态查看", "text_level": 1, "page_idx": 14}, {"type": "table", "img_path": "images/a2405a9883c8ff50896fbc6e8b16778ee7a8de7882faa967a8108ae3b93dc08c.jpg", "table_caption": ["6.2.1.4 secuLlama 服务/secullm 服务版本查看"], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-2003</td><td>需求名称</td><td>secuLlama服务状态查看</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">1、可以通过页面看到 secuLlama 服务/secullm 服务的状态，且与后台查询的状态一 致； 2、通过后台停止服务后，可以在页面更新状态，由于不需要数据库存储，每次页</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">面显示都是查询的最新状态，因此不存在延迟性。 不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">可以通过页面查看 secuLlama 服务的状态。该接口可压测。</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 14}, {"type": "table", "img_path": "images/e38ef2eb4bdb4ebcfa4092cb59998a0d813534ae00eb04406d0ee9460004699c.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-2004</td><td>需求名称</td><td>secuLlama服务版本查看</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td></td><td>1、可以通过页面看到 secuLlama 服务/secullm 服务的版本信息。</td><td></td><td></td><td></td></tr><tr><td>业务流程</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr><tr><td>输入输出约束</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 14}, {"type": "table", "img_path": "images/3832050036fa2c25964b76dc045adb8401c1dc5ac083c59270e6a25706734954.jpg", "table_caption": ["6.2.1.5 secuLlama 服务/secullm 服务接口手册下载"], "table_footnote": [], "table_body": "<html><body><table><tr><td>验收标准</td><td>可以通过页面查看secuLlama服务的版本信息。该接口可压测。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 15}, {"type": "table", "img_path": "images/3e4b6e2b2dbf1308a2d0c7c29b2a2e704021dccff77f3ae83bdcde62ab7cd750.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-2005</td><td>需求名称</td><td>下载接口手册</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">1、可以通过页面下载接口手册。</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">可以通过页面下载接口手册。</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 15}, {"type": "text", "text": "6.5.2. 模型管理", "text_level": 1, "page_idx": 15}, {"type": "text", "text": "6.5.2.1. 查看正在运行的模型", "page_idx": 15}, {"type": "table", "img_path": "images/1ed39cf473fe37f2cf2dcf5819538672dd45dea29b72fedfb9c9043eb42c7e03.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-2101</td><td>需求名称</td><td>查看正在运行的模型</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">可以通过页面查看正在运行模型有哪些。 需要提供后台新增模型的功能说明。</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">1、当 secuLlama 服务出于运行状态时，可以看到运行的模型； 2、当secuLlama 服务处于停止状态时，进入该菜单需要提示服务未运行。</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">根据 secuLlama 的服务不同状态，显示不同的内容。</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 15}, {"type": "text", "text": "6.5.3. a<PERSON><PERSON> 管理", "text_level": 1, "page_idx": 16}, {"type": "text", "text": "6.5.3.1. 查看 apikey ", "page_idx": 16}, {"type": "table", "img_path": "images/bae62961eafd467dc9722a7bd5781d6a3a4841a15b992c43839ffb49cc4c9713.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-2301</td><td>需求名称</td><td>查看 apikey</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">可以通过页面查看 apikey 列表，默认使用****表示，可以查看明文的apikey。</td></tr><tr><td>业务流程</td><td colspan=\"5\"></td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">1、默认情况下，apikey 以***表示 2、点击查看，可以看到明文的 apikey 3、提供复制apikey的功能按钮</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 16}, {"type": "text", "text": "6.5.4. 监控管理", "text_level": 1, "page_idx": 16}, {"type": "table", "img_path": "images/885cfe9c78281c51221d978cede6c168390fb2577320e3f0aa1567de776ebf5d.jpg", "table_caption": ["6.5.4.1. 监控面板开发 "], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-2201</td><td>需求名称</td><td>监控面板开发</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">结合指标汇总表，开发监控面板，用于显示监控指标</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">能够通过监控面板查看指标情况。</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 16}, {"type": "text", "text": "6.5.4.2. AI 一体机特殊指标梳理", "text_level": 1, "page_idx": 16}, {"type": "table", "img_path": "images/9668578debd781c0a2c45de9558e0d25b72e9acccccd39138f81c42decad2cd7.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-2202</td><td>需求名称</td><td>AI一体机特殊指标梳理</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">需要结合监控组件的特殊指标梳理需求，考虑是将指标采集的实现集成至监控组 件，还是监控管理模块；为了方便后续多台设备的监控管理，以及对接监管平台， 建议还是集成到监控组件，便于后续扩展。</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">无</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 17}, {"type": "text", "text": "6.5.4.3. 告警管理--本迭代由于不考虑数据库问题，暂不开发", "page_idx": 17}, {"type": "table", "img_path": "images/1f2751b305240379d94f3cb149a68eebc7663d2d9ef544823e75c2061547c941.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-2203</td><td>需求名称</td><td>告警管理</td><td>优先级</td><td>低</td></tr><tr><td>需求描述</td><td colspan=\"3\">通过内嵌监控组件的告警管理列表，实现告警功能，包含告警规则、告 警联系人、告警列表、邮件服务器配置、告警历史等。</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">告警管理功能正常。</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 17}, {"type": "text", "text": "6.5.5. 安全认证", "text_level": 1, "page_idx": 17}, {"type": "text", "text": "6.5.5.1. 基于 Ukey 的认证", "page_idx": 17}, {"type": "table", "img_path": "images/5c50fe7d8042b67e23614250c59e2e67c2c7fc5205bbe7d0e7ba022c0d92d89a.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-2301需求名称基于Ukey 的认证</td><td></td><td></td><td>优先级</td><td>高</td></tr></table></body></html>", "page_idx": 17}, {"type": "table", "img_path": "images/b259252d3e73537259b1d490f0e2b1e3f66fb358ea3a8363f40dd4ad82ce5e50.jpg", "table_caption": ["6.5.5.2. 基于配置文件的认证"], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求描述</td><td>考虑不使用数据库，利用内置密码卡的密钥对 Ukey下发证书，后续登录时，利用 密码卡中的密钥对证书进行验签即可。</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>使用正确的 Ukey 可以成功登录管理系统；使用错误的Ukey 无法登录。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 18}, {"type": "table", "img_path": "images/97ed2e89d03b4c5b586a9754dbfa74cefe803831e379d938c10b71e07bf89a05.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-2302</td><td>需求名称</td><td>基于配置文件的认证</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">通过配置文件可以登录管理系统，具体展示形式、实现方式待定。</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">通过配置文件可以登录管理系统。</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 18}, {"type": "text", "text": "6.6. 智能应用统一门户相关需求 ", "text_level": 1, "page_idx": 18}, {"type": "text", "text": "6.6.1. 基础网关相关需求", "text_level": 1, "page_idx": 18}, {"type": "text", "text": "基础网关作为用户访问智能应用统一门户的前置代理，主要功能如下：", "page_idx": 18}, {"type": "text", "text": "1. 用户身份认证：对所有访问统一门户的用户进行身份认证和权限控制2. 门户管理代理：门户管理员配置门户时，基础网关负责将请求转发到后台管理服务中，完成对门户的配置3. 门户访问代理：为每一个智能应用统一门户提供独立的访问地址", "page_idx": 18}, {"type": "text", "text": "4. 应用访问代理：用户访问应用时，基础网关负责将用户请求转发到对应的 Dify实例中，完成用户、应用、模型的交互", "page_idx": 19}, {"type": "text", "text": "6.6.1.1. 用户身份和用户类型", "text_level": 1, "page_idx": 19}, {"type": "table", "img_path": "images/6bfd1abce253974906c97d3e866f947998e0541474614079c281340acd72cef1.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>用户身份和用户类型</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">1.用户身份：即用户登录门户后系统为用户分配的 token，是鉴权时获取用户信息、 用户类型的凭证 2.用户类型分为四类： a.系统管理员：super用户，负责Dify 实例管理、门户管理、应用模板管理 b.aduit用户：用于查看审计日志和业务日志 c.门户管理员：oper用户，一个门户至少有一个门户管理员，负责应用管理、门户 用户管理、Dify控制台管理 d.门户用户：普通用户，可以访问某个门户，使用门户下的应用</td></tr><tr><td>业务流程 输入輸出約束</td><td colspan=\"3\">不涉及 不涉及</td><td></td><td></td></tr><tr><td></td><td colspan=\"3\">不同类型的用户可以访问对应的页面</td><td colspan=\"2\"></td></tr><tr><td>验收标准 其它说明</td><td colspan=\"3\">不涉及</td><td colspan=\"2\"></td></tr></table></body></html>", "page_idx": 19}, {"type": "text", "text": "6.6.1.2. ⻔户管理服务 API 代理", "text_level": 1, "page_idx": 19}, {"type": "table", "img_path": "images/56de06b32b38bb06d0dffdd2f0470b54286b20c88fdb36d68a625a204f64510e.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6512</td><td>需求名称</td><td>门户管理服务 API代理</td><td>优先级</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan=\"3\">1.通过基础网关，实现门户管理服务 API的访问代理：前端调用门户管理服务API 时，需要先经过基础网关，基础网关进行请求转发，转发至智能门户系统。 说明：鉴权交由统一 web 平台</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 19}, {"type": "table", "img_path": "images/81740800d6ad889cf92970b0fe8f515634ea1230ac89d851308e0d2b7220efc6.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>验收标准</td><td>登录成功后，访问前端页面时，可以正常访问；未登录时，通过复制url 无法访问。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 20}, {"type": "text", "text": "6.6.1.3. 门户访问代理和鉴权 ", "text_level": 1, "page_idx": 20}, {"type": "table", "img_path": "images/013e661d1b60c4cef84d19900072472dd580f1ab994c0e4944ba56e3d0cf5631.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6512</td><td>需求名称</td><td>门户访问代理</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">1.门户访问代理：当门户用户访问门户时，通过基础网关引导用户跳转到正确的门 户地址。 2.门户访问鉴权：基础网关不进行鉴权，仅转发。</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">1.门户用户能跳转到正确的门户页面</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 20}, {"type": "text", "text": "6.6.1.4. 应用访问代理和鉴权 ", "text_level": 1, "page_idx": 20}, {"type": "table", "img_path": "images/4ebd195b70128ce3be2cfc959f899816cba36c0f48dc679108daea90358fe04c.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6512</td><td>需求名称</td><td>应用访问代理</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">1.应用访问代理：当门户用户访问应用时，通过基础网关引导用户跳转到正确的应 用地址 2.应用访问鉴权：基础网关不鉴权，仅转发，鉴权由统一web 平台处理。</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">1.门户用户登录系统后，能跳转到正确的应用页面。 2.门户用户未登录系统，通过复制url 的方式无法跳转至应用页面。</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 20}, {"type": "text", "text": "6.6.1.5. 动态更新", "text_level": 1, "page_idx": 20}, {"type": "table", "img_path": "images/37422c4267f0bdfb3ec527c8ef80e3439de96086f249e4b598bd62be003e147d.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6512需求名称</td><td></td><td>动态更新路由</td><td>优先级</td><td>高</td></tr></table></body></html>", "page_idx": 20}, {"type": "table", "img_path": "images/d08036054ec667f1e73da723da43809e279ec7e3f094fb0744b58da4aae5c869.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求描述</td><td>当访问具体 dify 实例应用的请求到达基础网关后，基础网关先判断当前的路由配置 信息是否包含该dify 实例，如果包含，则直接转发；如果不包含，则查询数据库， 如果数据库中有，则更新路由，继续转发；如果数据库中也没有，返回404.</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>Dify 实例存在时，门户用户访问具体应用，访问成功； Dify实例不存在时，门户用户访问报错。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 21}, {"type": "text", "text": "6.6.2. 门户管理员管理", "text_level": 1, "page_idx": 21}, {"type": "text", "text": "6.6.2.1. 新增⻔户管理员", "text_level": 1, "page_idx": 21}, {"type": "table", "img_path": "images/f767c289477b28afce09e02c4fcbfabeb92c00398081055ee7441af2702d9878.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>新增门户管理员</td><td>优先级</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员 1.系统管理员可新增门户管理员账号，新增时必须绑定所属门户 2.一个门户可以绑定多个门户管理员</td></tr><tr><td>业务流程</td><td colspan=\"5\">3.门户管理员关联到 Dify 实例中的管理员账号，具有Dify 实例访问权限 不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">用户新增成功</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 21}, {"type": "text", "text": "6.6.2.2. 编辑⻔户管理员", "text_level": 1, "page_idx": 21}, {"type": "table", "img_path": "images/c3cc9098a57a14729b3283a55b66f41b655720ee3564b790f4f5c5d800a56f05.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>编辑门户管理员</td><td>优先级</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan=\"3\">权限归属：系统管理员 1.系统管理员可编辑门户管理员基本信息，不可更改所属门户</td><td></td><td></td></tr></table></body></html>", "page_idx": 21}, {"type": "table", "img_path": "images/ded9b68fc36b3d22c8e4eaad3b26b1cbfb0b56c8bfdbd30a30980b2778293bde.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>用户编辑成功</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 22}, {"type": "text", "text": "6.6.2.3. 删除⻔户管理员", "text_level": 1, "page_idx": 22}, {"type": "table", "img_path": "images/357e47033b8865780f46dc948db1a9f6cb9509d792017b4293004e494e5ee516.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>删除门户管理员</td><td>优先级</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员 1.系统管理员可删除门户管理员</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>輸入输出約束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">用户删除成功</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 22}, {"type": "text", "text": "6.6.2.4. ⻔户管理员列表", "text_level": 1, "page_idx": 22}, {"type": "table", "img_path": "images/f9f459c014f3474a3c020a7fc5b270695e103c1a310d6208aacdc30aa06ad7f6.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户管理员列表</td><td>优先级</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员 1.列表展示： －分页展示所有门户的管理员信息，默认按创建时间倒序排列。 －显示字段：用户名、用户类型、所属门户、最后登录时间。 2.筛选与搜索： －支持按用户类型、所属门户组合筛选。 -支持模糊搜索用户名 3.操作列：</td></tr></table></body></html>", "page_idx": 22}, {"type": "table", "img_path": "images/99b7937387c617f2c8af15f17659da5e392835811f42b6d66cf4119ab5f8b132.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td></td><td>－提供“编辑”、“删除”按钮，按钮状态根据用户权限动态显示</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 23}, {"type": "text", "text": "6.6.2.5. ⻔户管理员登录", "text_level": 1, "page_idx": 23}, {"type": "table", "img_path": "images/095772f3a586f246d86f244f1125420f788137c72dfc42ae7c1522e094d78b93.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户管理员登录</td><td>优先级</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan=\"5\">1.登录流程： －支持用户名、密码登录方式。 -密码错误3次后锁定账号5 分钟，需联系系统管理员解锁 2.Token机制： -登录成功生成 JWTToken，有效期 24小时，过期前15 分钟可自动续期</td></tr><tr><td>业务流程</td><td colspan=\"3\">－单用户允许多个活跃会话，以支持多端访问 不涉及</td><td colspan=\"2\"></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td colspan=\"2\"></td></tr><tr><td>验收标准</td><td colspan=\"3\"></td><td colspan=\"2\"></td></tr></table></body></html>", "page_idx": 23}, {"type": "text", "text": "6.6.2.6. ⻔户管理员退出", "text_level": 1, "page_idx": 23}, {"type": "table", "img_path": "images/1a07046f7fc115f181d9167a2d78192823634667859093dd5439eb0fc66ecd21.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户管理员退出</td><td>优先级</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan=\"3\">1.主动退出： －用户点击退出后，服务端销毁 Token 2.被动退出：</td><td></td><td></td></tr></table></body></html>", "page_idx": 23}, {"type": "table", "img_path": "images/e7f6c6fe25debfbfb7593e4ac829278e13b77289cfc262b4eca7be0af6c4a5f6.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td></td><td>- token 过期，用户下次鉴权失败后跳转至登录页</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 24}, {"type": "text", "text": "6.6.3. 门户用户管理", "text_level": 1, "page_idx": 24}, {"type": "text", "text": "6.6.3.1. 新增用户", "text_level": 1, "page_idx": 24}, {"type": "table", "img_path": "images/76f2407830dacebfd0b6724240027ca4fc0f29efddd738793eb029543565203c.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>新增用户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员、门户管理员 1.系统管理员可新增门户用户，新增时必须绑定所属门户 2.门户管理员可新增门户用户，新增时将用户自动添加至当前门户管理员 3.门户用户信息包括： -用户名称 -登录密码</td></tr><tr><td>业务流程</td><td colspan=\"3\">-所属门户管理员 不涉及</td><td></td><td></td></tr><tr><td>輸入輸出約束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">用户新增成功</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 24}, {"type": "text", "text": "6.6.3.2. 编辑用户", "text_level": 1, "page_idx": 25}, {"type": "table", "img_path": "images/6d227e5dc4ab8dd1f6a0202c41250cd8725e8f7c728cc0e52e7ed9d14baa9f47.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>编辑用户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">权限归属：系统管理员、门户管理员 1.系统管理员可编辑门户用户，修改所属门户管理员 2.门户管理员可编辑门户用户，不可更改所属门户管理员</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">3.可修改内容项：重置密码</td><td></td><td></td></tr><tr><td>输入輸出约束</td><td colspan=\"3\">不涉及 不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">用户密码重置成功，可以使用新密码登录</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 25}, {"type": "text", "text": "6.6.3.3. 删除用户 ", "text_level": 1, "page_idx": 25}, {"type": "table", "img_path": "images/4fb3e888e0494f44c591b2dd083fe78ff9ed082f973c9b4e0c28e4b073403b7c.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>删除用户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td>权限归属：系统管理员、门户管理员 1.系统管理员可删除门户用户 2.门户管理员可删除门户用户</td><td></td><td></td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">用户删除成功</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 25}, {"type": "text", "text": "6.6.3.4. ⻔户用户列表", "text_level": 1, "page_idx": 25}, {"type": "table", "img_path": "images/d49424b252484b7a4fd22b06e421c9bc61d2f376b83a327119db8043625eecd7.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户用户列表</td><td>优先级</td><td>高</td></tr></table></body></html>", "page_idx": 25}, {"type": "table", "img_path": "images/8ec4433095801c271a0e18af32154b05cc99c02331ef6b0e9409aef3bd10fd6d.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求描述</td><td>权限归属：系统管理员、门户管理员 1.列表展示： －分页展示所有门户用户，默认按创建时间倒序排列。 －显示字段：用户名、认证模式、所属门户管理员、状态（启用/禁用）、最后登录 时间。 2.筛选与搜索： －支持按所属门户管理源、状态组合筛选。</td></tr><tr><td>业务流程</td><td>-提供“编辑”、“禁用/启用”、“删除”按钮，按钮状态根据用户权限动态显示</td></tr><tr><td></td><td>不涉及</td></tr><tr><td>输入输出约束 验收标准</td><td>不涉及 用户列表显示成功，输入搜索条件，可显示符合条件的数据。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 26}, {"type": "text", "text": "6.6.3.5. ⻔户用户登录", "text_level": 1, "page_idx": 26}, {"type": "table", "img_path": "images/942abc77c815252401cff02c111daab9ba16a2f7aada4b33764b1ec3ba112234.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户用户登录</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">1.登录流程： －支持用户名、密码登录方式。 -密码错误5次后锁定账号1分钟 -统一web 平台进行用户名、密码验证成功后，智能门户进行用户状态的鉴权，如 果用户处于禁用状态，提示联系管理员启用；如果处于启用状态，重定向到该用户 所属的门户首页 2. Token 机制： -登录成功生成JWTToken，有效期半小时</td></tr><tr><td>业务流程</td><td>-单用户允许多个活跃会话，以支持多端访问 不涉及</td><td colspan=\"3\"></td></tr></table></body></html>", "page_idx": 26}, {"type": "table", "img_path": "images/23eb987c6d46cc287f5c9eee054aa045729e91d0bd930d35558d376f23e2faef.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>启用状态用户登录成功；禁用状态用户登录后进行提示。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 27}, {"type": "text", "text": "6.6.3.6. ⻔户用户退出", "text_level": 1, "page_idx": 27}, {"type": "table", "img_path": "images/2c8fcbf2e1ad49fc082205ec9e3724715a7dd6d275d2e31f3383e81ebce720f8.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户用户退出</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">1.主动退出： －用户点击退出后，服务端销毁 Token 2.被动退出： - token 过期时，用户下次鉴权失败后跳转至登录页</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>输入輸出約束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">用户退出系统成功，返回登录页面。</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 27}, {"type": "text", "text": "6.6.3.7. 启用⻔户用户 ", "text_level": 1, "page_idx": 27}, {"type": "table", "img_path": "images/72f310714b52a27408e60161cf9c030922f467e2ccb729fcc405e6dcc2a7644e.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>启用门户用户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">1．启用用户： －新增用户时，可以设置用户为启用状态，只有状态为“启用”的用户才能访问统一 门户 -支持从用户列表中批量选择用户，设置启用状态</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>輸入輸出約束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">用户启用成功，可以登录。</td></tr></table></body></html>", "page_idx": 27}, {"type": "table", "img_path": "images/db663d0701fd104e6822b9a8063507cd200d91d2089a597ff5c1850329cbfad8.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 28}, {"type": "text", "text": "6.6.3.8. 禁用⻔户用户", "text_level": 1, "page_idx": 28}, {"type": "table", "img_path": "images/ea422d60b554f0bdb689b1decc85729cfe222c1a1cf473dd7651e66aa74bced7.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>禁用门户用户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">1.禁用用户： -新增用户时，可以设置用户为禁用状态，状态为“禁用”的用户无法访问统一门户 -支持从用户列表中批量选择用户，设置禁用状态</td></tr><tr><td>业务流程</td><td colspan=\"5\">－被禁用的用户，无法登录统一门户，已有Token 立即失效 不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">用户禁用成功，未登录的客户端登陆后进行提示；已登录的客户端进行提示。</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 28}, {"type": "text", "text": "6.6.4. 门户管理（Dify 实例管理）", "text_level": 1, "page_idx": 28}, {"type": "text", "text": "6.6.4.1. 创建⻔户", "text_level": 1, "page_idx": 28}, {"type": "table", "img_path": "images/e55185b05ff846e05a8e5c0bb29933a74ac229ee1dec3eb3a3f05d868a1d350e.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>创建门户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员 1.系统管理员创建门户时，需填写： -门户名称 -门户标题、图标、logo、访问地址前缀、主题 －首页配置：选择默认应用</td></tr></table></body></html>", "page_idx": 28}, {"type": "table", "img_path": "images/d19b8b832b95756a8fc1ebc7eb686ecebcf56bed0be2238196e08e295627a38a.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td></td><td>3.门户状态：初始化中、初始化失败、运行中、已停止、启动中、异常、删除中、 删除失败</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>门户创建完成后，能通过门户访问地址进入门户</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 29}, {"type": "text", "text": "6.6.4.2. 编辑⻔户信息", "text_level": 1, "page_idx": 29}, {"type": "table", "img_path": "images/9c620b8c2ab0891a32394ce76809e86b26ac77d9cd359d7cd1aace358035fefd.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>编辑门户信息</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员、门户管理员 1.修改门户名称、门户标题、图标、logo、主题</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">用户编辑成功</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 29}, {"type": "text", "text": "6.6.4.3. ⻔户列表", "text_level": 1, "page_idx": 29}, {"type": "table", "img_path": "images/264b0ccd2c281589786dfd6965703156463f5e9129f68602f817c73f051f00ed.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户列表</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员 1.列表功能： －分页展示所有门户，显示字段：门户名称、状态（创建中/创建失败/运行中/已停 止/异常）、关联实例 －支持按状态、门户名称筛选 2.快捷操作：</td></tr></table></body></html>", "page_idx": 29}, {"type": "table", "img_path": "images/880986df02fbfd94ff940b691e7d83f56bbd8b10d0f88fc3007c5bd5c993d336.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出約束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>门户列表正常加载</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 30}, {"type": "text", "text": "6.6.4.4. 启动⻔户 ", "text_level": 1, "page_idx": 30}, {"type": "table", "img_path": "images/976dca6c77850d70b8cdffbc830c9e705079b46ba758b0f897a36eb5ced59c3d.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户列表</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员、门户管理员 1.启动流程 -在门户列表页操作“启动”门户 -后端自动拉起门户对应的 Dify 实例 -后端更新门户状态为已启动、异常</td></tr><tr><td>业务流程</td><td colspan=\"3\">-后端周期检测Dify 实例的健康状态，连续 3 次检测失败则标记为“异常” 不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">启动后门户状态变为运行中，且门户网站能正常访问</td><td colspan=\"2\"></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td colspan=\"2\"></td></tr></table></body></html>", "page_idx": 30}, {"type": "text", "text": "6.6.4.5. 停⽌⻔户 ", "text_level": 1, "page_idx": 30}, {"type": "table", "img_path": "images/ca77d16a1d51fe021d16b8c40344e0dbb3d3e244c6d23760bbf363f965e5428f.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>停止门户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员、门户管理员 1.停止流程 -在门户列表页操作“停止”门户</td></tr><tr><td>业务流程</td><td colspan=\"3\">-后端自动停止门户对应的 Dify 实例，并将门户状态更新为已停止 不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 30}, {"type": "table", "img_path": "images/e3a17a5815d5a892a57a3c748df012806dfa08aee1da15856398c3c27e8d3837.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>验收标准</td><td>停止后门户状态变为已停止，且门户网站无法访问</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 31}, {"type": "text", "text": "6.6.4.6. 删除⻔户 ", "text_level": 1, "page_idx": 31}, {"type": "table", "img_path": "images/5678f8d36ed9cf55f08f7415e15f412f4421f8ec92981da4918547649dbfcfc2.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>删除门户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员 1.状态为创建中、运行中、删除中的门户不支持删除 2.删除流程： -在门户列表页操作“删除”门户 -前端给出二次确认 -后端开始删除门户，并将门户状态改为“删除中” -后端删除 Dify 实例对应的容器 －后端删除 Dify 实例下产生的数据：应用、历史会话、知识库、数据库</td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td colspan=\"2\"></td></tr><tr><td>輸入輸出约束</td><td colspan=\"3\">不涉及 门户删除后管理员无法看到门户信息，门户用户无法访问该门户，服务器上该门户</td><td colspan=\"2\"></td></tr><tr><td>验收标准</td><td colspan=\"3\">对应的Dify实例数据已删除</td><td colspan=\"3\"></td></tr></table></body></html>", "page_idx": 31}, {"type": "text", "text": "6.6.4.7. 跳转管理端", "text_level": 1, "page_idx": 31}, {"type": "table", "img_path": "images/f898cc0f94b22d273ed7f0a4456a65645570cf47d82404e5a1118b3af5dca391.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>跳转管理端</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员、门户管理员 1.在门户列表页点击门户右侧“跳转管理端”按钮，浏览器打开Dify 管理端页面 2.免密登录：用户进入Dify 管理端页面无需登录，平台自动以管理员身份访问 Dify</td></tr><tr><td>业务流程</td><td colspan=\"5\">管理端页面 不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 31}, {"type": "table", "img_path": "images/695b4379e4660beddf04cd8fe0a0616283d6ebfbd14d0fb162d9a20a27cfeb0b.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>验收标准</td><td>浏览器能打开Dify 管理端页面</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 32}, {"type": "text", "text": "6.6.4.8. 跳转⻔户 ", "text_level": 1, "page_idx": 32}, {"type": "table", "img_path": "images/a28c456c7eb1820b2419e2439502beedb952fa5b2624a399b850830ad7979638.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>跳转管理端</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员、门户管理员 1.在门户列表页点击门户右侧“跳转门户”按钮，浏览器打开对应门户的首页</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">浏览器能打开对应门户的首页</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 32}, {"type": "text", "text": "6.6.4.9. ⾸⻚配置（迭代 2）", "text_level": 1, "page_idx": 32}, {"type": "table", "img_path": "images/243125808f83f373dafa886641969606b1be2573750f44d7b89c145602651ed3.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>首页配置</td><td>优先级</td><td>低</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员、门户管理员 用户进入门户首页，默认展示一个问答式应用，用户可直接使用 1.平台拉取Dify 实例中的现有问答式应用列表</td></tr><tr><td>业务流程</td><td colspan=\"5\">2.选择某个应用保存后，用户访问门户时，首页中使用的应用即默认应用 不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\"></td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 32}, {"type": "table", "img_path": "images/ce971766f12373533684512203922d01c98104085df9123f6cc60d12a1232f4b.jpg", "table_caption": ["6.6.4.10.访问控制配置（迭代 2）"], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>访问控制配置</td><td>优先级</td><td>低</td></tr><tr><td>需求描述</td><td>权限归属：系统管理员、门户管理员 2.授权访问：访问门户地址后，需要先登录才能访问</td><td>1.匿名访问：通过门户地址无需登录直接访问</td><td></td><td></td><td></td></tr><tr><td>业务流程</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr><tr><td>输入输出约束</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr><tr><td>验收标准</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>其它说明</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 33}, {"type": "text", "text": "6.6.5. Dify 容器化多实例部署改造", "text_level": 1, "page_idx": 33}, {"type": "text", "text": "6.6.5.1. Dify 多实例部署&网络隔离&目录隔离", "text_level": 1, "page_idx": 33}, {"type": "table", "img_path": "images/2988147708089aa0572972271a13267476a2074f16f186588d9cc7ce349c9adc.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>Dify多实例部署&amp;网络隔离&amp;目 录隔离</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">1.基于 Docker Compose 实现 Dify 容器化部署 2.实现在一台宿主机上部署多个实例 3.每个实例使用独立的网络命名空间</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">4.每个实例使用独立的磁盘目录</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及 不涉及</td><td colspan=\"2\"></td></tr><tr><td>验收标准</td><td colspan=\"6\">1.在门户管理端支持创建多个门户，在一体机中支持部署多套 Dify 实例 2.各Dify 实例中，知识库中的文档保存在宿主机中的独立目录中，不交叉存储</td></tr><tr><td>其它说明</td><td colspan=\"6\">不涉及</td></tr></table></body></html>", "page_idx": 33}, {"type": "text", "text": "6.6.5.2. 初始化供应商和模型", "text_level": 1, "page_idx": 34}, {"type": "table", "img_path": "images/96f448f98be1eb98c5094dbfd05a2807a4762c098231b7aa7b1b97c77b32aec3.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>初始化供应商和模型</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">权限归属：系统管理员 1. Dify 部署好后，自动完成供应商和模型的初始化 2.模型信息，通过 SecuLlama API 拉取</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">用户创建好统一门后， Dify 实例自动部署，Dify 中的供应商和模型自动完成初始化， 內置应用无需人工配置即可正常使用</td><td colspan=\"2\"></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td colspan=\"2\"></td></tr></table></body></html>", "page_idx": 34}, {"type": "text", "text": "6.6.5.3. Di<PERSON> 实例自动化部署", "text_level": 1, "page_idx": 34}, {"type": "table", "img_path": "images/66c7a3a3ac67403f48b119614ff8818c20d278c06dede096894681a05b2be9db.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>Dify 实例自动化部署</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：系统管理员 1.用户创建门户后，后台自动完成Dify 实例的部署、初始化，无需人工干预 2.后台将 Dify 实例的状态实时同步到门户中：初始化中、初始化失败、运行中、已</td></tr><tr><td>业务流程</td><td colspan=\"5\">停止、启动中、异常、删除中、删除失败 不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">用户创建好统一门后，Dify 实例自动部署，无需人工介入</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 34}, {"type": "text", "text": "6.6.6. 统一门户 ", "text_level": 1, "page_idx": 34}, {"type": "text", "text": "6.6.6.1. 访问⻔户⾸⻚", "text_level": 1, "page_idx": 34}, {"type": "table", "img_path": "images/827bf769adfcd27aac893ed9dba83b3cbaaef007e716cba45278c55722cf881e.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>访问门户首页</td><td>优先级</td><td>高</td></tr></table></body></html>", "page_idx": 34}, {"type": "text", "text": "权限归属：门户用户、门户管理员、系统管理员", "page_idx": 35}, {"type": "text", "text": "1. 门户用户通过登录页登录后，自动跳转到门户首页", "page_idx": 35}, {"type": "text", "text": "2. 门户管理员、系统管理员可在门户列表页中，点击后方的“跳转门户”，一键跳转到门户首页", "page_idx": 35}, {"type": "text", "text": "3. 在门户的任意页面点击门户 logo 后，自动跳转到门户首页", "page_idx": 35}, {"type": "image", "img_path": "images/cbbd187b94c8a86205b675334d4411d758cc700a4e4ff50406d55b03112d9592.jpg", "img_caption": [], "img_footnote": [], "page_idx": 35}, {"type": "text", "text": "6.6.6.2. 用户信息展示", "text_level": 1, "page_idx": 35}, {"type": "table", "img_path": "images/d114a9e69f4c0e6470ec1f894324aa32c783ac2e8a030610c62721915cb17c97.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户用户信息</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">权限归属：门户用户、门户管理员、系统管理员 1.在统一门户任意页面固定位置，展示当前登录的用户信息，例如用户名称</td><td colspan=\"2\"></td></tr></table></body></html>", "page_idx": 35}, {"type": "table", "img_path": "images/c0a2951e914f34a97e65c17bf68597acf9d2d1344f660934509d325a5a7a2f35.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td></td><td></td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入輸出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 36}, {"type": "text", "text": "6.6.6.3. ⻔户⾸⻚对话", "text_level": 1, "page_idx": 36}, {"type": "table", "img_path": "images/f19b2aafa0e2944a6cd0746529a0db9a24c2c196ce413dc30d526275daaf821c.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户首页对话</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：门户用户、门户管理员、系统管理员 1.用户进入门户首页后，自动为用户展示门户默认的应用，用户可以直接使用该应 用进行交互式对话 2.门户首页应用支持用户查看在当前应用的会话历史列表，可选择某历史会话后继 续对话</td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\"></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td></tr></table></body></html>", "page_idx": 36}, {"type": "text", "text": "6.6.6.4. 应用探索（⻔户应用列表）", "text_level": 1, "page_idx": 36}, {"type": "table", "img_path": "images/ab12dd279be84f4ee2a7fd1b2f68abc9346eab90535e72c5910f405005a0e592.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户应用列表</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">权限归属：门户用户、门户管理员、系统管理员 1.用户进入探索页后，页面显示该门户对应 Dify 实例中的应用（应用探索页只展 示已启用的应用） 2.应用按照标签分组展示，每个应用展示内容有应用名称、logo、应用简介</td></tr></table></body></html>", "page_idx": 36}, {"type": "text", "text": "3. 标签来自 Dify 管理后台中的预置数据和门户管理员修改的数据", "page_idx": 37}, {"type": "text", "text": "4. 点击应用后跳转到应用对话页", "page_idx": 37}, {"type": "text", "text": "5. 支持用户收藏某应用", "page_idx": 37}, {"type": "table", "img_path": "images/fa08d7551a44232603aff8ae3b3a67c024c1c4f736a1583eaed21479957ee223.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td rowspan=\"3\">Q</td><td>SANSEC智能助 +新建会话 CttK</td><td colspan=\"7\">Strah Jotinan</td></tr><tr><td rowspan=\"7\">QSANSEC 探索班 长文生成据 PPT助手 学术接索 代码助手</td><td colspan=\"7\">探索SANSEC+ 我的算顶</td></tr><tr><td>育方推荐</td><td>办公效率</td><td></td><td>社交媒乐 生活实用</td><td></td><td></td><td></td></tr><tr><td></td><td></td><td>SANSEC版</td><td></td><td></td><td>长文生成万学长文</td><td></td></tr><tr><td></td><td></td><td></td><td>PPT助手 Kmi+APPT=-生成PPT</td><td>SEUPTO</td><td>学术接索</td><td>ESANEEO</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>业务流程</td><td>不涉及</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>输入輸出约束</td><td>不涉及</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>验收标准</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 37}, {"type": "text", "text": "6.6.6.5. 应用对话⻚ ", "text_level": 1, "page_idx": 37}, {"type": "table", "img_path": "images/3b817ff986e053aec6280cf73ebbf6d444646c0de6ce7b3a12c84517278e51fa.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>应用对话页</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">权限归属：门户用户、门户管理员、系统管理员 1.用户点击快捷访问栏中的应用，进入应用对话页 2.在应用对话页，用户可以直接使用该应用进行交互式对话 3.应用会话页支持用户查看当前应用的会话历史列表，可选择某历史会话后继续对</td><td></td><td></td></tr></table></body></html>", "page_idx": 37}, {"type": "image", "img_path": "images/7c8ee2a6aa90e3be348752ec383f3239e27244034e8e9d8c8605305a1a9876dd.jpg", "img_caption": [], "img_footnote": [], "page_idx": 38}, {"type": "text", "text": "6.6.7. 预置应用", "text_level": 1, "page_idx": 38}, {"type": "text", "text": "6.6.7.1. 通用助手", "text_level": 1, "page_idx": 38}, {"type": "table", "img_path": "images/330d9295efba2427249e9aae17759e4993a30b022efd310ff93d5745865f5f77.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>通用助手</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td>通用智能助手，支持带有记忆的对话、支持上传文档进行问答</td><td></td><td></td><td></td><td></td></tr><tr><td>业务流程</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr><tr><td>输入输出约束</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 38}, {"type": "table", "img_path": "images/d15ec8efcc6552eda628e60d69aa7c6ae4983f6aa78b1b7a9ff7d397185af617.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 39}, {"type": "text", "text": "6.6.7.2. 图片转文字", "text_level": 1, "page_idx": 39}, {"type": "table", "img_path": "images/0d76ab7266c773c03669982e4733d4703a5e83ac9246947af25e77a4c6927e76.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>图片转文字</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">上传图片，识别图中文字</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\"></td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 39}, {"type": "text", "text": "6.6.7.3. 英文翻译官", "text_level": 1, "page_idx": 39}, {"type": "table", "img_path": "images/6106d47b39fceb3d68bbf52f53ac7868a43a004369cd410cefab20ca86e56a06.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>英文翻译官</td><td>优先級</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">支持中英文互相翻译</td><td colspan=\"2\"></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td colspan=\"2\"></td></tr><tr><td>輪入輸出約束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\"></td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 39}, {"type": "text", "text": "6.6.7.4. 文章润色", "text_level": 1, "page_idx": 39}, {"type": "table", "img_path": "images/e522d89b13c886b4ef1e63e8adae85d0d8bd4ab0b95bdd051ec2a24c98f7d0c8.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>文章润色</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">选择需要的语言风格、字数，对原有文章进行润色</td><td></td><td></td></tr></table></body></html>", "page_idx": 39}, {"type": "table", "img_path": "images/54cab0f335234162173b1436c8712c1f52b1d88b10df3e99f9e72e5da0ea6aa5.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 40}, {"type": "text", "text": "6.6.7.5. 图表生成器", "text_level": 1, "page_idx": 40}, {"type": "table", "img_path": "images/ebd6ba346a8c7daa914cb4c32cde7bd09fd0d59e73e4e7455eb6465e3155f3f2.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>图表生成器</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">使用自然语言将数据或表格生成折线图、柱状图、饼图。</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\"></td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 40}, {"type": "text", "text": "6.6.7.6. 英文全书翻译", "text_level": 1, "page_idx": 40}, {"type": "table", "img_path": "images/fbe4f4630e17ff7f24da3a4ee3bf0440469ad9c631e35ba038e29cf01ca320ff.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>英文全书翻译</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">英文全书翻译</td><td colspan=\"2\"></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td colspan=\"2\"></td></tr><tr><td>輸入輸出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"3\"></td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 40}, {"type": "text", "text": "6.6.7.7. 公文写作", "text_level": 1, "page_idx": 40}, {"type": "table", "img_path": "images/f0d730c425ebca143b86cc3bffcfca388c84fd6a7c8265aeab1dbd995aeed456.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>公文写作</td><td>优先级</td><td>高</td></tr></table></body></html>", "page_idx": 40}, {"type": "table", "img_path": "images/8c5507d63bab6b1a80a96594f1b24d021ae7ba02646745f0c1cbe7ecd108414b.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求描述</td><td>根据特定主题和內容生成 15 种法定格式的公文</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 41}, {"type": "text", "text": "6.6.7.8. 编码助手", "text_level": 1, "page_idx": 41}, {"type": "table", "img_path": "images/fa5c229190f33184e421c5cfa4abc59d734f87a7db6862cb333a363e0482df08.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>编码助手</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">编程专家，可以实现多种编程语言的代码生成、智能注释、代码纠错、错误解析、 多语言转换等功能。</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\"></td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 41}, {"type": "text", "text": "6.6.7.9. UI 设计助手", "text_level": 1, "page_idx": 41}, {"type": "table", "img_path": "images/a29d018451df18671164fcb5e2220e059b66184255ecdcd9d8ace52f9ab1e192.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>UI设计助手</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">根据自然语言描述生成html 代码，可以点击预览</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出約束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\"></td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 41}, {"type": "text", "text": "6.6.7.10.latex 公式编辑&识别", "text_level": 1, "page_idx": 42}, {"type": "table", "img_path": "images/030c59b11d44f4a04c07603a193d18f0051bc02a45bd789339b9b38622831de1.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>latex公式编辑&amp;识别</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">支持自然语言描述生成 latex 格式的公式并进行渲染；支持识别图片中的公式（每 次上传一条公式），生成图中公式的latex代码。</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>输入输出約束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\"></td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 42}, {"type": "text", "text": "6.6.7.11.Arxiv 论文助手", "text_level": 1, "page_idx": 42}, {"type": "table", "img_path": "images/68e79748d88ff3551fd85c2a25b617b461ce06904556cbeb40ee9e24baa99a23.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>Arxiv论文助手</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">使用自然语言查询 Arxiv 网站上的论文</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>输入输出約束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\"></td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 42}, {"type": "text", "text": "6.6.7.12.数据库查询助手", "text_level": 1, "page_idx": 42}, {"type": "table", "img_path": "images/200c7ea6e05f471ec4207c0c9435c69ec5c80d353d5a9286c4a4e4ed1d597789.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>数据库查询助手</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td></td><td>使用自然语言查询数据库，可以查询数据库结构和数据库中表的数据。需要使用 URI与数据库进行连接，支持 mysql、oracle、oraclellg、postgresql 或mssql。</td><td colspan=\"3\"></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td colspan=\"2\"></td></tr></table></body></html>", "page_idx": 42}, {"type": "table", "img_path": "images/7d1f1fe2ac9b5d05457536872186a46b9dbecc0fe542994ec67a2e3d3259c58f.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 43}, {"type": "text", "text": "6.6.7.13.shell 指令助手", "text_level": 1, "page_idx": 43}, {"type": "table", "img_path": "images/f371c776058b586173cbfd775850e5d1ad8cc6fe47ac0c1b07ea4c1586384b09.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>shell指令助手</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">根据自然语言生成相应的 shell 指令，并指定某个指定在目标服务器上执行 敏感指令会被拦截</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>输入输出約束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\"></td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 43}, {"type": "text", "text": "6.7. 数据围栏系统", "text_level": 1, "page_idx": 43}, {"type": "text", "text": "为保障企业的数据安全和隐私安全，一体机提供一系列针对敏感数据的识别和处置方案，其中包括敏感数据识别算法，数据脱敏处理方式，业务自定义的配置选项和海量数据处理能力。能够应用多种隐私合规标准，对原始数据进行分级打标、判断敏感级别和实施相应的脱敏处理。", "page_idx": 43}, {"type": "text", "text": "随着大语言模型在文本生成、对话交互等场景的广泛应用，其输出内容可能涉及政治、暴力、色情等敏感信息，存在合规风险。为满足法律法规及企业安全要求，需要在一体机内部构建内容审核组件，实时检测并拦截敏感内容，确保输出内容的安全性与合规性。", "page_idx": 43}, {"type": "text", "text": "6.7.1 数据脱敏组件", "text_level": 1, "page_idx": 43}, {"type": "table", "img_path": "images/9103d542f2b25256eedc413aba7578636fee619b9e39254ba3ac968f7e5d8328.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6701</td><td>需求名称</td><td>数据脱敏组件</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">数据脱敏组件需要提供对敏感数据进行识别和处置的能力。</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">不涉及</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 44}, {"type": "text", "text": "6.7.2 内容审核组件", "text_level": 1, "page_idx": 44}, {"type": "table", "img_path": "images/1b077afa3bf81bb68731e2aa071f6934698145fd4a47ba1fe3174fcb06574181.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6702</td><td>需求名称</td><td>内容审核组件</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">内容审核组件需要对涉及政治、暴力、色情等敏感信息的内容进行检测和拦截。</td></tr><tr><td>业务流程</td><td colspan=\"5\">不涉及</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">不涉及</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 44}, {"type": "text", "text": "6.7.3 组件集成", "text_level": 1, "page_idx": 44}, {"type": "table", "img_path": "images/e380e449e8ec65ba448ed33dec9e3e7caf936b7d1b2ad64e5d858fe46b44e307.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6703</td><td>需求名称</td><td>数据脱敏与内容审核组件集成</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">1.将数据脱敏组件与内容审核组件集成为数据围栏系统； 2.数据围栏系统以 docker形式部署在一体机中； 3.数据围栏系统以 restful api 的方式提供服务；</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\">4.能够测试数据围栏服务是否正常工作； 不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">1.可通过查看正在运行的，确数据系统</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\">不涉及</td><td></td><td></td></tr></table></body></html>", "page_idx": 44}, {"type": "text", "text": "6.7.4 数据围栏系统启用与禁用", "text_level": 1, "page_idx": 44}, {"type": "table", "img_path": "images/5430c5492836fb54852dedf0a174c02ef474612b96505337f734198f6b0a1a54.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6704</td><td>需求名称</td><td>数据围栏系统启用与禁用</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">1.预置应用默认启用数据围栏系统； 2.新建应用可以选择是否启用数据围栏系统；</td></tr><tr><td>业务流程</td><td colspan=\"5\">3.已安装应用允许对数据围栏的启用和禁用进行修改；</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及 不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">1.预置应用的数据国样系统关数开系统，</td></tr></table></body></html>", "page_idx": 44}, {"type": "table", "img_path": "images/61bc5473d57f6b2369e8b694b71f6d0fae0f8ad5007c69768a19f46e5e1aabae.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td></td><td>3.能够对已安装应用的数据围栏系统进行启用与禁用；</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>", "page_idx": 45}, {"type": "text", "text": "6.8. 产品外观需求 ", "text_level": 1, "page_idx": 45}, {"type": "table", "img_path": "images/d9efa783b6eb06629a3f64b516c3309dab2d019341941d1a4c4b5d02b2c70c1e.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-F-6801</td><td>需求名称</td><td>服务器铭牌、外观需求</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"3\">外购整机需要： 1、铭牌：粘贴铭牌 2、外观要求：粘贴公司相关商标、标签</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan=\"3\"></td><td></td><td></td></tr><tr><td>输入輸出约束</td><td colspan=\"3\"></td><td></td><td></td></tr><tr><td>验收标准</td><td colspan=\"3\">外观满足正常出货要求</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan=\"3\"></td><td></td><td></td></tr></table></body></html>", "page_idx": 45}, {"type": "text", "text": "6.9. 接口需求", "text_level": 1, "page_idx": 45}, {"type": "text", "text": "", "page_idx": 45}, {"type": "text", "text": "6.10. 界面需求 ", "page_idx": 45}, {"type": "text", "text": "6.11. 性能需求", "text_level": 1, "page_idx": 45}, {"type": "text", "text": "", "text_level": 1, "page_idx": 45}, {"type": "text", "text": "6.12. 可靠性/可用性需求", "text_level": 1, "page_idx": 45}, {"type": "text", "text": "", "page_idx": 46}, {"type": "text", "text": "", "page_idx": 46}, {"type": "text", "text": "", "page_idx": 46}, {"type": "text", "text": "", "page_idx": 46}, {"type": "table", "img_path": "images/84d3685a5b277b72599cfbeee1d5ec81150fe870367c97bdbadbc3b94ca6bd78.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td><td>验收标准</td></tr><tr><td>PR-R-0001</td><td>压测7天</td><td>压测7天能够稳定运行</td><td>低</td><td>压测正常</td></tr></table></body></html>", "page_idx": 46}, {"type": "text", "text": "", "page_idx": 46}, {"type": "text", "text": "$\\textit { I } .$ 需求编号采用 $P R - R - * * * *$ 的形式， PR 代表产品， $R$ 代表可靠性需求， 编号从 0001$\\mathcal { L } .$ 填写表格时需要按照需求的层次自行增加子章节。", "page_idx": 46}, {"type": "text", "text": "6.13. 安全性需求", "text_level": 1, "page_idx": 46}, {"type": "text", "text": "", "page_idx": 46}, {"type": "text", "text": "1. 利用可靠的密码技术。  \n2. 掌握特定的记录或历史数据集。  \n3. 给不同的模块分配不同的功能。  \n4. 限定一个程序中某些区域的通信。  \n5. 计算临界值的检查和。", "page_idx": 46}, {"type": "text", "text": "", "page_idx": 46}, {"type": "text", "text": "", "page_idx": 46}, {"type": "text", "text": "", "page_idx": 47}, {"type": "text", "text": "$\\textit { I } .$ 需求编号采用 $P R - S - * * * *$ 的形式， $P R$ 代表产品， $S$ 代表安全需求， 编号从0001$\\mathcal { L } .$ 填写表格时需要按照需求的层次自行增加子章节。", "page_idx": 47}, {"type": "text", "text": "6.13.1 知识库加密 ", "text_level": 1, "page_idx": 47}, {"type": "table", "img_path": "images/c7fa882be0fe5ce44b05fdc1f7a44eab77354f2be87de6a76d7024711c66b4be.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>PR-S-0001</td><td>需求名称</td><td>知识库加密</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan=\"5\">实现对知识库中所有文档原始文件的透明加密： 1.用户对知识库文档上传、文档搜索、内容搜索、内容编辑、删除等操作均为无感 操作</td></tr><tr><td>业务流程</td><td colspan=\"5\">2.仅授权用户（包括用户从属进程）才能访问文档原始文件</td></tr><tr><td>输入输出约束</td><td colspan=\"5\">不涉及 不涉及</td></tr><tr><td>验收标准</td><td colspan=\"5\">加密作不影响识户对的识库的操作</td></tr><tr><td>其它说明</td><td colspan=\"5\">不涉及</td></tr></table></body></html>", "page_idx": 47}, {"type": "text", "text": "6.14. 可维护性需求", "text_level": 1, "page_idx": 47}, {"type": "text", "text": "", "page_idx": 47}, {"type": "table", "img_path": "images/982cc41aedf17598a73ac17f7662119b3a8dd94cc3991f78aff968ee1b03ec54.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td><td>验收标准</td></tr><tr><td>PR-M-0001</td><td>服务端日志</td><td>在系统发生运行故障，或运算错误等情 况，需要有日志记录，能够定位问题。</td><td></td><td></td></tr></table></body></html>", "page_idx": 47}, {"type": "text", "text": "", "page_idx": 47}, {"type": "text", "text": "需求编号采用 PR-M-\\*\\*\\*\\*的形式， $P R$ 代表产品， M 代表可维护性需求， 编号从开始依次累加。  \n$\\mathcal { L } .$ 填写表格时需要按照需求的层次自行增加子章节。", "page_idx": 47}, {"type": "text", "text": "6.15. 工作状态需求", "text_level": 1, "page_idx": 47}, {"type": "text", "text": "", "page_idx": 48}, {"type": "table", "img_path": "images/d0f1d228caadcf99f29ff785d0521060ba08238b19b6fe37b72638dcd2814ffa.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td><td>验收标准</td></tr><tr><td>PR-WS-0001</td><td></td><td></td><td></td><td></td></tr><tr><td>PR-WS-0002</td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 48}, {"type": "text", "text": "", "page_idx": 48}, {"type": "text", "text": "1． 需求编号采用 PR-WS-\\*\\*\\*\\*的形式， $P R$ 代表产品， WS 代表工作状态， 编号从 0001  \n2． 填写表格时需要按照需求的层次自行增加子章节。", "page_idx": 48}, {"type": "text", "text": "6.16. 结构需求", "text_level": 1, "page_idx": 48}, {"type": "text", "text": "", "page_idx": 48}, {"type": "table", "img_path": "images/9e232a6adfbea887c0d32bb61f0d0d5e137d62caad3b61e4c99e8486bdde37d7.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-ST-0001</td><td>密码卡 扩展槽</td><td>外购4 卡/8卡整机，在2 网卡、1RAID 卡之外，需要额外的 PCIE 卡槽安装 62 型密码卡。</td><td>高</td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 48}, {"type": "text", "text": "", "page_idx": 48}, {"type": "text", "text": "1． 需求编号采用 PR-ST-\\*\\*\\*\\*的形式， PR 代表产品， ST 代表结构需求， 编号从 0001  \n2． 填写表格时需要按照需求的层次自行增加子章节。", "page_idx": 48}, {"type": "text", "text": "6.17. 环保需求 ", "text_level": 1, "page_idx": 48}, {"type": "text", "text": "", "page_idx": 49}, {"type": "text", "text": "无特殊要求，依赖供应商的产品规格。", "page_idx": 49}, {"type": "table", "img_path": "images/d73df4a60dfec67c0bdfb367a490a693d6dcd99f50e08c12acef14cf1ef3e183.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0001</td><td></td><td></td><td></td></tr><tr><td>PR-E-0002</td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 49}, {"type": "text", "text": "", "page_idx": 49}, {"type": "text", "text": "$P R - E - * * * *$ $E$ ", "page_idx": 49}, {"type": "text", "text": "", "page_idx": 49}, {"type": "text", "text": "6.18. 认证需求 ", "text_level": 1, "page_idx": 49}, {"type": "text", "text": "", "page_idx": 49}, {"type": "table", "img_path": "images/b7da6a8415dd0593b70295d013d2a930c60030eb0e36dc16b760333519704d21.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>认证等级</td><td>接口规范</td><td>优先级</td></tr><tr><td>PR-C-0001</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>PR-C-0002</td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 49}, {"type": "text", "text": "", "page_idx": 49}, {"type": "text", "text": "$I .$ $P R - C - * * * *$ $P R$ $C$ $\\mathcal { L } .$ ", "page_idx": 49}, {"type": "text", "text": "6.19. 用户文档需求", "text_level": 1, "page_idx": 49}, {"type": "text", "text": "", "page_idx": 49}, {"type": "table", "img_path": "images/3264846ba5113623b6073f1654f8f5f0304a1cdf730f5e18e12b1d2a4a671475.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描 述</td><td>优先级</td></tr><tr><td>PR-D-0001</td><td>用户指南v1.0.0</td><td></td><td></td></tr></table></body></html>", "page_idx": 49}, {"type": "text", "text": "注：", "page_idx": 49}, {"type": "text", "text": "需求编号采用 PR-D-\\*\\*\\*\\*的形式，PR 代表产品，D 代表文档需求，编号从 0001开始依次累加。", "page_idx": 49}, {"type": "text", "text": "填写表格时需要按照需求的层次自行增加子章节。", "page_idx": 50}, {"type": "text", "text": "6.20. 客户特殊需求 ", "text_level": 1, "page_idx": 50}, {"type": "table", "img_path": "images/f2b226a435f5da8da05b77ea56a6c5ceeebdff89b4d29bf70a92dff43952d350.jpg", "table_caption": [""], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td><td>验收标准</td></tr><tr><td>PR-CS-0001</td><td></td><td></td><td></td><td></td></tr><tr><td>PR-CS-0002</td><td></td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 50}, {"type": "text", "text": "6.21. 法律法规要求", "text_level": 1, "page_idx": 50}, {"type": "table", "img_path": "images/941f0ebf5b2242578f5b4d865b552a214b65d239c00806b73479ad89fdeb54a4.jpg", "table_caption": [""], "table_footnote": ["注：以上所列通用法律法规，若本产品不满足或有其他标准，请自行修改或添加。"], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-LR-0001</td><td>中华人民共和国密码法</td><td>满足相关要求</td><td>高</td></tr><tr><td>PR-LR-0002</td><td>计算机信息网络国际联网 安全保护管理办法</td><td>满足相关要求</td><td>高</td></tr><tr><td>PR-LR-0003</td><td>网络安全技术生成式人 工智能服务安全基本要求 （TC260-003)</td><td>满足相关要求</td><td>高</td></tr></table></body></html>", "page_idx": 50}, {"type": "text", "text": "6.22. 国家及行业标准要求", "text_level": 1, "page_idx": 50}, {"type": "text", "text": "", "page_idx": 50}, {"type": "table", "img_path": "images/dd22f4a9cf1eea5498890ccb23022d46d4ac6145ce0bea61ac8c3dfc86d16fe3.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-NIS-0001</td><td>GB-T8567-2006计算机 软件文档编制规范</td><td>满足相关要求</td><td>高</td></tr><tr><td>PR-NIS-0002</td><td>GB-T9386-2008计算机 软件测试文档编制规范</td><td>满足相关要求</td><td>高</td></tr></table></body></html>", "page_idx": 50}, {"type": "text", "text": "注：以上所列行标，若本产品不满足或有其他标准，请自行修改或添加。", "page_idx": 50}, {"type": "text", "text": "6.23. 失效模式分析(参见《设计失效模式和影响分析(DFMEA)库》)", "text_level": 1, "page_idx": 50}, {"type": "text", "text": "6.24. 其他需求", "text_level": 1, "page_idx": 50}, {"type": "text", "text": "", "page_idx": 51}, {"type": "table", "img_path": "images/7b170700ad3138e840b135e5a4eca76d352c2944dd542d9426f56b6a048e2f21.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-O-0001</td><td></td><td></td><td></td></tr><tr><td>PR-O-0002</td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 51}, {"type": "text", "text": "", "page_idx": 51}, {"type": "text", "text": "需求编号采用 $P R = 0 - * * * *$ 的形式， $P R$ 代表产品， $O$ 代表其他需求， 编号从 0001", "page_idx": 51}, {"type": "text", "text": "$\\mathcal { L } .$ 填写表格时需要按照需求的层次自行增加子章节。", "page_idx": 51}]