from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse, HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi import Request
import uvicorn
from services.report_analyzer import ReportAnalyzer
from services.document_parser import DocumentParser
from services.model_service import ModelService
from services.config import Config
import os
import json
import asyncio
from dotenv import load_dotenv
from typing import Optional

load_dotenv()
config = Config()

app = FastAPI(title="需求及设计文档评审助手")

# 设置模板目录
current_dir = os.path.dirname(os.path.abspath(__file__))
templates = Jinja2Templates(directory=os.path.join(current_dir, "templates"))
# 新增静态文件挂载
app.mount("/static", StaticFiles(directory=os.path.join(current_dir, "templates")), name="static")

# 初始化服务
model_service = ModelService()
document_parser = DocumentParser()
report_analyzer = ReportAnalyzer(model_service, document_parser)

# 全局调试信息存储
debug_messages = []
debug_clients = []

def add_debug_message(message: str, level: str = "info"):
    """添加调试信息"""
    return
    import time
    debug_msg = {
        "timestamp": time.time(),
        "message": message,
        "level": level
    }
    debug_messages.append(debug_msg)

    # 只保留最近100条消息
    if len(debug_messages) > 100:
        debug_messages.pop(0)

    # 通知所有连接的客户端
    for client_queue in debug_clients:
        try:
            client_queue.put_nowait(debug_msg)
        except:
            pass


# ==================== 页面路由 ====================

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/debug-stream")
async def debug_stream():
    """SSE端点，用于实时推送调试信息"""
    import queue
    import time

    client_queue = queue.Queue()
    debug_clients.append(client_queue)

    async def event_generator():
        try:
            # 发送历史消息
            for msg in debug_messages[-10:]:  # 只发送最近10条
                yield f"data: {json.dumps(msg)}\n\n"

            # 持续发送新消息
            while True:
                try:
                    # 等待新消息，超时1秒
                    msg = client_queue.get(timeout=1)
                    yield f"data: {json.dumps(msg)}\n\n"
                except queue.Empty:
                    # 发送心跳
                    yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"
                except Exception as e:
                    print(f"SSE error: {e}")
                    break
        finally:
            # 清理客户端连接
            if client_queue in debug_clients:
                debug_clients.remove(client_queue)

    return StreamingResponse(
        event_generator(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

@app.post("/analyze")
async def analyze_document(
    document_file: UploadFile = File(...),
    document_type: str = Form("requirement")  # requirement, design, feasibility
):
    try:
        add_debug_message(f"收到文件: {document_file.filename}, 文档类型: {document_type}", "info")

        # 验证文档类型
        if document_type not in ["requirement", "design", "feasibility"]:
            return JSONResponse(
                status_code=400,
                content={"error": "不支持的文档类型，支持的类型：requirement, design, feasibility"}
            )

        # 获取当前文件所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        uploads_dir = os.path.join(current_dir, "uploads")

        # 保存上传的文档文件
        file_path = os.path.join(uploads_dir, document_file.filename)
        os.makedirs(uploads_dir, exist_ok=True)
        with open(file_path, "wb") as f:
            content = await document_file.read()
            f.write(content)

        add_debug_message(f"文件已保存到: {file_path}", "success")

        # 设置分析器的文档类型
        report_analyzer.set_document_type(document_type)

        # 根据文档类型选择分析方法
        add_debug_message(f"开始分析{document_type}文档...", "info")
        
        if document_type in ["requirement", "design"]:
            # 使用检查单分析
            result = report_analyzer.analyze_document_with_checklist(file_path)
        else:
            # 可研报告使用原有方法
            result = report_analyzer.analyze(file_path)
        
        add_debug_message("分析完成", "success")

        return JSONResponse(content=result)
    except Exception as e:
        error_msg = f"错误: {str(e)}"
        add_debug_message(error_msg, "error")
        print(error_msg)
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

# ==================== 文档评审API ====================

@app.get("/api/checklist/{document_type}")
async def get_checklist(document_type: str):
    """获取检查单信息"""
    try:
        if document_type == "requirement":
            checklist = document_parser.parse_requirement_checklist(config.REQUIREMENT_CHECKLIST())
            checklist_name = "需求评审检查单"
        elif document_type == "design":
            checklist = document_parser.parse_design_checklist(config.DESIGN_CHECKLIST())
            checklist_name = "设计评审检查表"
        else:
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": "不支持的文档类型"}
            )
        
        # 按分类统计
        categories = {}
        for item in checklist:
            category = item['category']
            if category not in categories:
                categories[category] = 0
            categories[category] += 1
        
        return JSONResponse(content={
            "success": True, 
            "data": {
                "checklist_name": checklist_name,
                "total_items": len(checklist),
                "categories": categories,
                "items": checklist[:10]  # 只返回前10项作为预览
            }
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

# ==================== 运行服务器 ====================

if __name__ == "__main__":
    import sys
    port = 8000
    if len(sys.argv) > 2 and sys.argv[1] == "--port":
        port = int(sys.argv[2])
    uvicorn.run(app, host="0.0.0.0", port=port)
