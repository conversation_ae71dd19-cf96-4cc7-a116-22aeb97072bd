#!/usr/bin/env python3
"""
贫困县查询MCP工具服务器
提供贫困县查询功能，用于验证报告中提到的地区是否为贫困县
"""

import asyncio
import json
import logging
from typing import Any, Dict, List
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
from mcp.server.fastmcp import FastMCP
from mcp.server.fastmcp.prompts.base import Message

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("poverty_county_query")
# 创建FastMCP实例
server = FastMCP("poverty_county_query")

# 模拟贫困县数据库
POVERTY_COUNTIES_DB = {
    "广西壮族自治区": {
        "横州市": {
            "is_poverty_county": False,
            "poverty_level": "非贫困县",
            "exit_poverty_year": None,
            "description": "横州市为南宁市代管县级市，经济发展良好，非国家级贫困县"
        },
        "上林县": {
            "is_poverty_county": True,
            "poverty_level": "国家级贫困县",
            "exit_poverty_year": 2020,
            "description": "上林县曾为国家级贫困县，2020年脱贫摘帽"
        },
        "马山县": {
            "is_poverty_county": True,
            "poverty_level": "国家级贫困县", 
            "exit_poverty_year": 2020,
            "description": "马山县曾为国家级贫困县，2020年脱贫摘帽"
        },
        "隆安县": {
            "is_poverty_county": True,
            "poverty_level": "国家级贫困县",
            "exit_poverty_year": 2019,
            "description": "隆安县曾为国家级贫困县，2019年脱贫摘帽"
        }
    },
    "河北省": {
        "承德县": {
            "is_poverty_county": True,
            "poverty_level": "国家级贫困县",
            "exit_poverty_year": 2020,
            "description": "承德县曾为国家级贫困县，2020年脱贫摘帽"
        }
    },
    "四川省": {
        "凉山彝族自治州": {
            "is_poverty_county": True,
            "poverty_level": "深度贫困地区",
            "exit_poverty_year": 2020,
            "description": "凉山彝族自治州为深度贫困地区，2020年全面脱贫"
        }
    }
}


# 使用装饰器注册工具
@server.tool()
async def query_poverty_status(province: str, county: str) -> str:
    """
    查询指定地区的贫困县状态信息
    参数:
        province: 省份名称，如：广西壮族自治区
        county: 县/市名称，如：横州市
    返回:
        JSON格式的查询结果，包含贫困县状态详情
    """
    # 参数处理
    province = province.strip()
    county = county.strip()
    
    if not province or not county:
        return "错误：省份和县名不能为空"
    
    # 查询数据库
    province_data = POVERTY_COUNTIES_DB.get(province)
    if not province_data:
        return f"未找到省份 '{province}' 的贫困县数据"
    
    county_data = province_data.get(county)
    if not county_data:
        return f"未找到 '{province}' 省 '{county}' 的贫困县数据"
    
    # 构建查询结果
    result = {
        "province": province,
        "county": county,
        "is_poverty_county": county_data["is_poverty_county"],
        "poverty_level": county_data["poverty_level"],
        "exit_poverty_year": county_data["exit_poverty_year"],
        "description": county_data["description"],
        "query_time": "2024-12-01"
    }
    
    return json.dumps(result, ensure_ascii=False, indent=2)

@server.tool()
async def search_poverty_counties(province: str, include_exited: bool = True) -> str:
    """
    搜索指定省份的所有贫困县信息
    参数:
        province: 省份名称
        include_exited: 是否包含已脱贫的县，默认为true
    返回:
        JSON格式的省份贫困县列表信息
    """
    # 参数处理
    province = province.strip()
    
    province_data = POVERTY_COUNTIES_DB.get(province)
    if not province_data:
        return f"未找到省份 '{province}' 的贫困县数据"
    
    # 筛选贫困县
    poverty_counties = []
    for county, data in province_data.items():
        if data["is_poverty_county"]:
            if include_exited or data["exit_poverty_year"] is None:
                poverty_counties.append({
                    "county": county,
                    "poverty_level": data["poverty_level"],
                    "exit_poverty_year": data["exit_poverty_year"],
                    "description": data["description"]
                })
    
    result = {
        "province": province,
        "total_poverty_counties": len(poverty_counties),
        "include_exited": include_exited,
        "counties": poverty_counties,
        "query_time": "2024-12-01"
    }
    
    return json.dumps(result, ensure_ascii=False, indent=2)

# 
def main():
    """启动MCP服务器"""
    logger.info("启动贫困县查询MCP服务器...")
    server.run(transport='stdio')

if __name__ == "__main__":
    main()
