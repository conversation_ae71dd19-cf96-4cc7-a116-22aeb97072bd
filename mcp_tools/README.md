# MCP智能体工具集

本目录包含了报告评审系统的MCP（Model Context Protocol）智能体工具集，用于增强大模型在报告评审过程中的数据查询和验证能力。

## 工具概述

### 1. 贫困县查询工具 (poverty_county_query)
- **功能**: 查询指定地区的贫困县状态信息
- **适用场景**: 验证报告中提到的地区是否为贫困县，用于政策符合性审查
- **主要功能**:
  - `query_poverty_status`: 查询特定县市的贫困状态
  - `search_poverty_counties`: 搜索省份内的所有贫困县

### 2. 资质证明查询验证工具 (qualification_verify)
- **功能**: 验证企业资质证明的真实性和有效性
- **适用场景**: 审查项目承建单位的资质是否符合要求
- **主要功能**:
  - `verify_company_qualification`: 验证企业特定资质
  - `query_company_info`: 查询企业基本信息和所有资质
  - `check_qualification_validity`: 检查资质证书有效性

### 3. 能源局估算单价表查询工具 (energy_price_query)
- **功能**: 查询各类能源设备的官方估算单价
- **适用场景**: 审查投资估算的合理性，验证设备价格
- **主要功能**:
  - `query_equipment_price`: 查询特定设备价格
  - `search_price_by_category`: 按类别搜索设备价格
  - `calculate_project_cost`: 计算项目总成本估算

## 安装和配置

### 1. 安装依赖
每个工具都是独立的Python项目，需要单独安装依赖：

```bash
# 为每个工具安装依赖
cd mcp_tools/poverty_county_query
pip install -r requirements.txt

cd ../qualification_verify
pip install -r requirements.txt

cd ../energy_price_query
pip install -r requirements.txt
```

### 2. 环境配置
在主项目的`.env`文件中启用MCP功能：

```env
MCP_ENABLED=true
```

### 3. 测试工具
使用测试脚本验证工具功能：

```bash
# 测试所有MCP工具
python debug/test_mcp_agent.py

# 测试单个工具
python debug/test_mcp_agent.py poverty_county_query

# 测试集成的模型服务
python debug/test_model_with_mcp.py
```

## 工具使用示例

### 贫困县查询
```python
result = await mcp_agent.call_tool(
    "poverty_county_query_query_poverty_status",
    {
        "province": "广西壮族自治区",
        "county": "横州市"
    }
)
```

### 资质验证
```python
result = await mcp_agent.call_tool(
    "qualification_verify_verify_company_qualification",
    {
        "company_name": "广西电网有限责任公司",
        "qualification_type": "电力工程施工总承包"
    }
)
```

### 设备价格查询
```python
result = await mcp_agent.call_tool(
    "energy_price_query_query_equipment_price",
    {
        "equipment_type": "变压器",
        "specification": "10kV"
    }
)
```

## 数据说明

### 贫困县数据
- 包含广西、河北、四川等省份的贫困县信息
- 数据包括贫困等级、脱贫年份等信息
- 数据为模拟数据，实际使用时应连接真实数据源

### 企业资质数据
- 包含主要电力企业的资质信息
- 涵盖施工总承包、设计等各类资质
- 包含证书编号、有效期等详细信息

### 能源设备价格数据
- 基于国家能源局2024年指导价格
- 包含变压器、输电线路、配电设备等
- 提供价格区间和标准价格

## 扩展开发

### 添加新工具
1. 在`mcp_tools/`目录下创建新的工具目录
2. 实现MCP服务器接口
3. 在`MCPAgentService`中添加工具配置
4. 更新提示词模板，说明新工具的使用方法

### 数据源集成
- 可以将模拟数据替换为真实的API接口
- 支持数据库连接和外部系统集成
- 建议实现数据缓存机制提高性能

## 注意事项

1. **工具调用原则**: 仅在需要验证具体数据准确性时调用工具
2. **性能考虑**: 工具调用会增加响应时间，应合理使用
3. **数据准确性**: 当前为模拟数据，生产环境需要连接真实数据源
4. **错误处理**: 工具调用失败时会返回错误信息，不影响主要评审流程

## 技术架构

```
报告评审系统
├── ModelService (模型服务)
│   ├── 集成MCP工具调用
│   └── 处理工具调用结果
├── MCPAgentService (MCP智能体服务)
│   ├── 管理多个MCP工具连接
│   └── 提供统一的工具调用接口
└── MCP工具集
    ├── poverty_county_query (贫困县查询)
    ├── qualification_verify (资质验证)
    └── energy_price_query (能源价格查询)
```

每个MCP工具都是独立的服务器进程，通过标准输入输出与主系统通信，实现了良好的模块化和可扩展性。
